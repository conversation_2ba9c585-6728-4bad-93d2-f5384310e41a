package com.parkingmanage.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.parkingmanage.common.Result;
import com.parkingmanage.entity.Role;
import com.parkingmanage.service.RoleService;
import com.parkingmanage.vo.RolePermVo;
import com.parkingmanage.vo.RoleSidebarVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 <p>
 角色 前端控制器
 </p>

 <AUTHOR>
 @since 2022-02-27
*/
@RestController
@RequestMapping("/parking/role")
public class RoleController {
    @Resource
    private RoleService roleService;

    @ApiOperation("添加")
    @PostMapping
    public ResponseEntity<Result> insertRole(@RequestBody Role role) {
        roleService.insertRole(role);
        return ResponseEntity.ok(new Result());
    }

    @ApiOperation("修改")
    @PutMapping
    public boolean update(@RequestBody Role role) {
        return roleService.updateById(role);
    }

    @ApiOperation("删除")
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return roleService.removeById(id);
    }

    @ApiOperation("查询所有")
    @GetMapping("/listAll")
    public List<Role> findAll() {
        return roleService.list();
    }

    @ApiOperation("查询单条")
    @GetMapping("/{id}")
    public Role findById(@PathVariable String id) {
        return roleService.getById(id);
    }

    @ApiOperation("保存角色权限")
    @PostMapping("/perm/{id}")
    public boolean updatePermById(@PathVariable Integer id, @RequestParam String permission) {
        return roleService.updatePermById(id, permission);
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public IPage<Role> findPage(@RequestParam(required = false) String name,
                                @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        return roleService.page(new Page<>(pageNum, pageSize), Wrappers.<Role>lambdaQuery().like(Role::getName, name));
    }

    @ApiOperation("查询权限")
    @GetMapping("/perm/{roleId}")
    public List<RolePermVo> findPermById(@PathVariable Integer roleId) {
        return roleService.findPermById(roleId);
    }

    @ApiOperation("根据用户角色id查询导航")
    @GetMapping("/sidebar/querySidebarById")
        //todo 无法记录上一次保存记录
    public List<RoleSidebarVo> querySidebarById(@RequestParam(value = "id") Integer id) {
        List<RoleSidebarVo> roleSidebarVos = roleService.findSidebarById(id);
        return roleSidebarVos;
    }

    // @ApiOperation("查询导航")
    // @GetMapping("/sidebar/{id}")
    // public List<RoleSidebarVo> findSidebarById(@PathVariable Integer id) {
    //     return roleService.findSidebarById(id);
    // }

    @ApiOperation("查询所有角色权限分配下拉框")
    @GetMapping("/noAdmin")
    public List<Role> findAllNoAdmin() {
        return roleService.list();
    }
}

