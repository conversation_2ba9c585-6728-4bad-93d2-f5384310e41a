package com.parkingmanage.mapper;

import com.parkingmanage.entity.Butler;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.parkingmanage.entity.Community;

import java.util.List;

/**
 <p>
  Mapper 接口
 </p>

 <AUTHOR>
 @since 2023-02-11
*/
public interface ButlerMapper extends BaseMapper<Butler> {
    Butler getButlerByOpenId(String openid);
    Butler getButlerByPhone(String phone);
    int duplicate(<PERSON> butler);
    Butler getByUsercode(String province, String city, String district, String community, String usercode);

    List<Integer> getManageArea(String province, String city, String district, String community, String usercode);
}
