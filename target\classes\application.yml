spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
#    password: <PERSON><PERSON>@271.HS
    url: *****************************************************************************************************************************************************************************
#    url: *****************************************************************************************************************************************************************************************************
#    url: *********************************************************************************************************************************************************************************************************
#    url: *****************************************************************************************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    jpa:
      generate-ddl: false
      hibernate:
        ddl-auto: none
      servlet:
        multipart:
          max-file-size: 100MB
          max-request-size: 100MB
#  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
mybatis-plus:
  mapper-locations: classpath:**/mapper/xml/*.xml
  type-aliases-package: com.parkingmanage.entity
  # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
  # 全局逻辑删除的实体
  global-config:
    db-config:
      logic-delete-field: deleted  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1      # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0    # 逻辑未删除值(默认为 0)
#文件存储位置
file:
  upload-path: D:/uploadfile/
  # 配置SpringMVC文件上传限制，默认1M。注意MB要大写，不然报错。SpringBoot版本不同，配置项不同
  spring:
    servlet:
      multipart:
        max-file-size: 50MB
        max-request-size: 50MB
server:
  port: 8543
  # SSL配置已注释，如需启用HTTPS请取消注释
#  ssl:
#     key-store-password: 2xffjgct    #填写jks-password.txt文件内的密码。
#     key-store-type: JKS #JKS格式证书密钥库类型。PFX格式的证书密钥库类型为PKCS12。
#     key-store: www.xuerparking.cn.jks   #您需要使用实际的证书名称替换domain_name.jks。
swagger:
  basePackage: com.parkingmanage
  title: 雪儿泊车管理系统
  description: 雪儿泊车管理系统
  version: V1.0
logging:
  file:
    path: /usr/local/parking_demo/logs
    file-max-size: 5242880
    name: ParkingDemo.log
    # 超时提醒专用日志文件
    max-size: 10MB
    max-history: 30
  charset:
    console: UTF-8
    file: UTF-8

# 百度AI配置
baidu:
  ai:
    # 从百度AI开放平台获取 (https://ai.baidu.com/)
    # 创建应用后在"管理中心"→"文字识别"→"车牌识别"中查看
    app-id: 103715637      # 应用ID
    api-key: 45kdY5Q4wyGLiFNUbOtU8j8U     # API Key
    secret-key: GBWTclI2hmOAYp4I1BvZLgxHWjXJbMU6 # Secret Key
    
    # API配置
    base-url: https://aip.baidubce.com   # 百度AI API基础URL
    token-url: /oauth/2.0/token          # 获取访问令牌URL
    plate-url: /rest/2.0/ocr/v1/license_plate # 车牌识别API URL
    
    # 缓存配置
    token-cache-minutes: 25              # 访问令牌缓存时间(分钟)，百度默认30天有效期

# 微信配置
wechat:
  # 微信小程序配置
  miniapp:
    appid: wx112d8a922018480e  # 替换为您的小程序AppID
    secret: INVALID_SECRET_DISABLE_WECHAT_API   # 暂时禁用微信API，使用普通二维码
    # access_token缓存时间（秒）
    token-cache-time: 7000
    # 是否启用微信小程序码（设为false使用普通二维码）
    enabled: false
  # 微信公众号配置（停车超时提醒功能）
  # 微信公众号AppID
  appid: wx112d8a922018480e
  # 微信公众号AppSecret
  secret: c4c0609fbbaf3ecb0a56314cea15f0c8
  # 超时提醒模板消息配置
  timeout:
    template:
      # 模板消息ID（您提供的实际模板ID）
      id: sjyHHEhrj24mFasi8FXaaz_ucOfuI1KvUUz8yeXMymY

# 停车超时提醒功能配置
parking:
  timeout:
    # 定时任务执行间隔（毫秒）默认5分钟
    check-interval: 300000
    # 提醒时间（分钟）- 在超时前多少分钟提醒
    warning-minutes: 15
    # 是否启用超时提醒功能
    enabled: true
    # 默认超时时间配置（分钟）
    default-timeout:
      temp: 120      # 临时车辆2小时
      visitor: 180   # 访客车辆3小时  
      owner: 720     # 业主车辆12小时
    # 重试配置
    retry:
      # 最大重试次数
      max-attempts: 3
      # 重试间隔（毫秒）
      delay: 30000