package com.parkingmanage.service;

import com.parkingmanage.entity.Butler;
import com.baomidou.mybatisplus.extension.service.IService;
import com.parkingmanage.entity.Community;
import com.parkingmanage.entity.Member;
import com.parkingmanage.entity.Ownerinfo;

import java.util.List;

/**
 <p>
  服务类
 </p>

 <AUTHOR>
 @since 2023-02-11
*/
public interface ButlerService extends IService<Butler> {
    Butler getButlerByOpenId(String openid);
    <PERSON> getButlerByPhone(String phone);
    int duplicate(<PERSON> butler);
    List<Butler> queryListButler(String username, String community);
    Butler getByUsercode(String province, String city, String district, String community, String usercode);

    List<Integer> getManageArea(String province, String city, String district, String community, String usercode);
}
