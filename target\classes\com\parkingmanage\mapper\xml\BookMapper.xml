<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.BookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Book">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="time" property="time" />
        <result column="date" property="date" />
        <result column="img" property="img" />
        <result column="file" property="file" />
        <result column="price" property="price" />
        <result column="pages" property="pages" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, time, date, img, file, price, pages
    </sql>
    <select id="getBookByOpenId" resultType="com.parkingmanage.entity.Book">
        select
        from book a
        where a.id=#{id}
    </select>
    <select id="duplicate" resultType="java.lang.Integer">
        select count(*) from book  where name=#{name} and time=#{time}
        and price=#{price} and date=#{date}
        <if test="id!=null and id !=''">
            and id &lt;&gt;#{id}
        </if>
    </select>

</mapper>
