package com.parkingmanage.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.parkingmanage.common.AIKEResult;
import com.parkingmanage.common.HttpClientUtil;
import com.parkingmanage.common.Result;
import com.parkingmanage.common.config.AIKEConfig;
import com.parkingmanage.entity.*;
import com.parkingmanage.service.*;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.Controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/parking/vehicleReservation", method = {RequestMethod.GET, RequestMethod.POST})
public class VehicleReservationController {

    public static final HashMap<String, String> HASH_MAP_GATE = new HashMap<>();
    @Resource
    private VehicleReservationService vehicleReservationService;

    @Autowired
    public AIKEConfig aikeConfig;

    @Autowired
    private ChannelInfoService channelInfoService;

    @Autowired
    private YardInfoService yardInfoService;

    @Autowired
    private PreVipTypeService preVipTypeService;

    @Autowired
    private VehicleClassificationService vehicleClassificationService;

    @Autowired
    private AppointmentService appointmentService;

    @Resource
    private ReportCarInService reportCarInService;

    @Resource
    private ReportCarOutService reportCarOutService;

    @Setter
    @Getter
    private String enterPreVipType = "";

    private Logger logger = LoggerFactory.getLogger(Controller.class);

    @ApiOperation("添加")
    @PostMapping("insert")
    public ResponseEntity<Result> insertVehicleReservation(@RequestBody VehicleReservation vehicleReservation) {
        vehicleReservation.setCreateTime(new Date());
        vehicleReservation.setAppointmentTime(new Date());
        vehicleReservation.setAppointmentFlag(0);
        VehicleReservation vehicleReservation1 = vehicleReservationService.selectByCarName(vehicleReservation.getPlateNumber());
        Result result = new Result();
        if (vehicleReservation1 == null) {
            vehicleReservationService.save(vehicleReservation);
            result.setCode("0");
            result.setMsg("添加成功！");
        } else {
            result.setCode("1");
            result.setMsg("数据重复，增加失败！");
        }
        return ResponseEntity.ok(result);
    }

    @ApiOperation("修改")
    @PostMapping("update")
    public ResponseEntity<Result> update(@RequestBody VehicleReservation vehicleReservation) {
        int num = vehicleReservationService.duplicate(vehicleReservation);
        Result result = new Result();
        if (num == 0) {
            vehicleReservationService.updateById(vehicleReservation);
            result.setCode("0");
            result.setMsg("修改成功！");
        } else {
            result.setCode("1");
            result.setMsg("数据重复，修改失败！");
        }
        return ResponseEntity.ok(result);
    }

    @ApiOperation("删除")
    @PostMapping("/{id}")
    public ResponseEntity<Result> delete(@PathVariable String id) {
//        if(vehicleReservationService.removeById(id))
        boolean success = vehicleReservationService.removeById(id);
        if (!success) {
            return ResponseEntity.ok(Result.error("删除失败！"));
        }
        return ResponseEntity.ok(Result.success("删除成功！"));
    }

    @ApiOperation("批量删除")
    @PostMapping("/batchDelete")
    public ResponseEntity<Result> batchDelete(@RequestBody List<Integer> ids) {
        boolean success = vehicleReservationService.removeByIds(ids);
//        logger.info("success = " + success);
        if (!success) {
            return ResponseEntity.ok(Result.error("批量删除失败！"));
        }
        return ResponseEntity.ok(Result.success("批量删除成功！"));
    }

    @ApiOperation("查询所有")
    @GetMapping("/getAllVehicleReservation")
    public List<VehicleReservation> getAllVehicleReservation() {
        List<VehicleReservation> myquery = vehicleReservationService.list();
        return myquery;
    }

    @ApiOperation("清空超时车辆")
    @GetMapping("/timeOutCleanUp")
    public List<TimeOutVehicleList> timeOutCleanUp(Integer timeOutInterval) {
        ArrayList<TimeOutVehicleList> vehicleReservations = new ArrayList<>();
        // 调用SQL查询超时数据
        vehicleReservations = vehicleClassificationService.selectBytimeOutInterval(timeOutInterval);
        return vehicleReservations;
    }

    @ApiOperation("入场超时车辆查询")
    @GetMapping("/enterTimeOutCleanUp")
    public List<TimeOutVehicleList> enterTimeOutCleanUp(Integer timeOutInterval, String yardName) {
        ArrayList<TimeOutVehicleList> vehicleReservations = new ArrayList<>();
        // 调用SQL查询超时数据,将30分钟和小时进行区分
        vehicleReservations = vehicleClassificationService.selectByEnterTimeOutInterval(timeOutInterval, yardName);
        return vehicleReservations;
    }


    @ApiOperation("分页查询")
    @GetMapping("/page")
    public IPage<VehicleReservation> findPage(@RequestParam(required = false) String plateNumber, @RequestParam(required = false) String yardName, @RequestParam(required = false, defaultValue = "1") Integer pageNum, @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Page<VehicleReservation> vehicleReservationPage = new Page<>(pageNum, pageSize);
        QueryWrapper<VehicleReservation> vehicleReservationQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(plateNumber)) {
            vehicleReservationQueryWrapper.like("plate_number", plateNumber);
        }
        if (StringUtils.isNotBlank(yardName)) {
            vehicleReservationQueryWrapper.like("yard_name", yardName);
        }
        vehicleReservationQueryWrapper.eq("reserve_flag", 0);
        return vehicleReservationService.page(vehicleReservationPage, vehicleReservationQueryWrapper);
    }

    @ApiOperation("放行的分页查询")
    @GetMapping("/reservationPage")
    public IPage<VehicleReservation> findReservationPage(@RequestParam(required = false) String plateNumber, @RequestParam(required = false) String yardName, @RequestParam(required = false, defaultValue = "1") Integer pageNum, @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Page<VehicleReservation> vehicleReservationPage = new Page<>(pageNum, pageSize);
        QueryWrapper<VehicleReservation> vehicleReservationQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(plateNumber)) {
            vehicleReservationQueryWrapper.like("plate_number", plateNumber);
        }
        if (StringUtils.isNotBlank(yardName)) {
            vehicleReservationQueryWrapper.like("yard_name", yardName);
        }
        vehicleReservationQueryWrapper.eq("reserve_flag", 1);
        vehicleReservationQueryWrapper.orderByDesc("appointment_time");
        return vehicleReservationService.page(vehicleReservationPage, vehicleReservationQueryWrapper);
    }

    @ApiOperation("添加入场")
    @PostMapping("/addReservation")
    public ResponseEntity<Result> addReservation(@RequestBody VehicleReservation vehicleReservation) {//已经预约触发开闸
        //调用开关闸的接口参数
        HashMap<String, String> hashMapGate = new HashMap<>();
        hashMapGate.put("parkCode", vehicleReservation.getYardCode());
        hashMapGate.put("channelCode", vehicleReservation.getChannelName());
        hashMapGate.put("opType", "0");
        hashMapGate.put("operator", "自动操作员");
        String getGet = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/openGates", hashMapGate);
//        logger.info(vehicleReservation.getPlateNumber() + "手动抬杆开关闸：" + getGet);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        VehicleReservation reservation = vehicleReservationService.getById(vehicleReservation.getId());
        reservation.setReserveTime(dateFormat.format(new Date()));
        reservation.setReserveFlag(1);
        //若是手动放行的话，则se入场时间显示"手动放行"，放行时间显示当前时间
        reservation.setEnterTime("手动放行");
        vehicleReservationService.updateById(reservation);
        Result result = new Result();
        result.setMsg("添加入场成功！");
        result.setCode("0");
        return ResponseEntity.ok(result);
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/export")
    public void export(HttpServletResponse response, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate, @RequestParam("yardName") String yardName, @RequestParam("channelName") String channelName) throws IOException, ParseException {
        vehicleReservationService.exportVehicleReservation(startDate, endDate, yardName, channelName, response);
    }

    @ApiOperation("开关闸机")
    @GetMapping("/openGates")
    public ResponseEntity openGates(String parkCode, String channelCode, Integer opType, String operator) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("parkCode", parkCode);
        params.put("channelCode", channelCode);
        params.put("opType", opType);
        params.put("operator", operator);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String operateTime = simpleDateFormat.format(new Date());
        params.put("operateTime", operateTime);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "opGate", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("获取授权停车场编码列表")
    @GetMapping("/getParkInfo")
    public ResponseEntity getParkInfo(String parkCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("parkCode", parkCode);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getParkInfo", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("获取授权停车场通道列表")
    @GetMapping("/getChannelInfo")
    public ResponseEntity getChannelInfo(String parkCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("parkCode", parkCode);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getChannelInfo", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("获取授权停车场通道列表")
    @GetMapping("/aikeGetChannelInfo")
    public List<ChannelInfo> aikeGetChannelInfo(String yardCode) {
        // 调用接口查询车场名称
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("parkCode", yardCode);
        System.out.println("hashMap = " + hashMap);
        String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/getChannelInfo", hashMap);
//        String get = HttpClientUtil.doGet("http://localhost:8543/parking/vehicleReservation/getChannelInfo", hashMap);
        List<ChannelInfo> channelInfos = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(get);
        JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
        JSONObject jsonObjectDataData = (JSONObject) jsonObjectData.get("data");
        JSONArray jsonObjectDataDataChannel = (JSONArray) jsonObjectDataData.get("recordList");
        String entranceName = "";
        for (int i = 0; i < jsonObjectDataDataChannel.size(); i++) {
            JSONObject jsonObjectChannelInfoList = (JSONObject) jsonObjectDataDataChannel.get(i);
            entranceName = (String) jsonObjectChannelInfoList.get("entranceName");
            String customCode = (String) jsonObjectChannelInfoList.get("customCode");
            Integer id = (Integer) jsonObjectChannelInfoList.get("id");
            ChannelInfo channelInfo = new ChannelInfo();
            channelInfo.setChannelId(String.valueOf(id));
            channelInfo.setChannelName(entranceName);
            channelInfo.setParkCode(customCode);
            channelInfos.add(channelInfo);
        }
        // 将查询到的结果存储到数据库中
        // 先查询一下，若有就直接查询，没有添加
        ChannelInfo channelInfo = channelInfoService.channelByName(entranceName);
        if (channelInfo == null) {
            channelInfoService.saveBatch(channelInfos);
        }
        return channelInfos;
    }

    @ApiOperation("获取进场时间段进场记录")
    @PostMapping("/getCarInList")
    public ResponseEntity getCarInList(String parkCode, String isPresence, String startTime, String endTime, int pageNum, int pageSize) throws ParseException {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("parkCode", parkCode);
        params.put("isPresence", isPresence);
        //将enterTime转为yyyy-MM-dd HH:mm:ss
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        params.put("startTime", simpleDateFormat.format(startTime));
        params.put("endTime", simpleDateFormat.format(endTime));
        params.put("pageNum", pageNum);
        params.put("pageSize", 1000);
//        logger.info("传入的参数startTime为：" + simpleDateFormat.format(startTime));
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getCarInList", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("查询车牌线上月票开通记录")
    @GetMapping("/getOnlineMonthTicketByCarCard")
    public ResponseEntity getOnlineMonthTicketByCarCard(String carCode, int pageNum, int pageSize) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("carCode", carCode);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getOnlineMonthTicketByCarCard", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("通过月票ID查询线上月票开通记录")
    @GetMapping("/getOnlineVipTicket")
    public ResponseEntity getOnlineVipTicket(String monthTicketId) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("monthTicketId", monthTicketId);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getOnlineVipTicket", params);
//        logger.info("调用aike接口后的通过月票ID查询线上月票开通记录" + data);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("获取停车场在场车辆")
    @GetMapping("/getParkOnSiteCar")
    public ResponseEntity getParkOnSiteCar(String parkCodeList, String enterTimeFrom, String enterTimeTo, String pageNum, String pageSize) throws ParseException {
        HashMap<String, Object> params = new HashMap<>();
        String formatEnterTimeFrom = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        params.put("parkCodeList", Arrays.asList(parkCodeList));
        params.put("enterTimeFrom", enterTimeFrom);
        params.put("enterTimeTo", enterTimeTo);
        params.put("pageNum", Integer.valueOf(pageNum));
        params.put("pageSize", Integer.valueOf(pageSize));
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getParkOnSiteCar", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("调用获取停车场在场车辆接口")
    @PostMapping("/getAKEParkOnSiteCar")
    public ResponseEntity<Result> getAKEParkOnSiteCar(@RequestBody CarOnSiteEntity carOnSiteEntity) throws ParseException {
        String formatEnterTimeFrom = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Result result = new Result();
        ArrayList<ParkOnSiteCar> parkOnSiteCars = new ArrayList<ParkOnSiteCar>();
        String resultParam = "";
        long diffInMillies = 0L;
        for (String parkingCodeList : carOnSiteEntity.getParkCodeList()) {
            for (int i = 0; i < 2; i++) {
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("parkCodeList", parkingCodeList);
                hashMap.put("enterTimeFrom", enterTimeFromAdd(convertDateFormat(String.valueOf(formatEnterTimeFrom))));
                hashMap.put("enterTimeTo", convertDateFormat(formatEnterTimeFrom));
                hashMap.put("pageNum", String.valueOf(i + 1));
                hashMap.put("pageSize", "1000");
                String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8111/aketest/getParkOnSiteCar", hashMap);
//                String get = HttpClientUtil.doGet("http://localhost:8543/parking/vehicleReservation/getParkOnSiteCar", hashMap);
//                logger.info("getMapAll = " + get);
                JSONObject jsonObject = JSONObject.parseObject(get);
                JSONObject jsonObjectDataCarIn = (JSONObject) jsonObject.get("data");
                JSONArray recordList = (JSONArray) jsonObjectDataCarIn.get("recordList");
                // 根据currCount进行遍历
//                System.out.println("currCount = " + jsonObjectDataCarIn.get("currCount"));
                for (int i1 = 0; i1 < recordList.size(); i1++) {
                    //每个对象进行存储
                    JSONObject jsonObject1 = recordList.getJSONObject(i1);
                    // 根据查询出来的预约表中的车牌号再进行存储
                    String enterCarLicenseNumber = (String) jsonObject1.getString("carNo");
                    if (!enterCarLicenseNumber.contains("未识别")) {
                        String string = jsonObject1.getString("enterTime");
                        String enterTime = convertDateFormatYY(string);
                        ParkOnSiteCar parkOnSiteCar = new ParkOnSiteCar();
                        parkOnSiteCar.setCarNo(jsonObject1.getString("carNo"));
                        parkOnSiteCar.setEnterTime(enterTime);
                        parkOnSiteCar.setParkName(jsonObject1.getString("parkName"));
                        // 计算停车时长
                        SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date enterTimeFormat = sdfTime.parse(enterTime);
                        Date leaveTimeFormat = sdfTime.parse(formatEnterTimeFrom);
                        diffInMillies = Math.abs(leaveTimeFormat.getTime() - enterTimeFormat.getTime());
                        long diffHours = diffInMillies / (60 * 60 * 1000);
                        long diffMinutes = (diffInMillies / (60 * 1000)) % 60;
                        long diffSeconds = (diffInMillies / 1000) % 60;
                        if (diffHours > 24) {
                            long days = diffHours / 24;
                            diffHours %= 24;
                            resultParam = days + "天" + diffHours + "小时" + diffMinutes + "分钟" + diffSeconds + "秒";
                        } else {
                            if (diffHours == 0) {
                                resultParam = diffMinutes + "分钟" + diffSeconds + "秒";
                            } else {
                                resultParam = diffHours + "小时" + diffMinutes + "分钟" + diffSeconds + "秒";
                            }
                        }
                        parkOnSiteCar.setParkingDuration(resultParam);
                        parkOnSiteCar.setParkingDurationTimes(diffInMillies);
                        parkOnSiteCars.add(parkOnSiteCar);
                    }
                }
            }
        }
        // 将30分钟、2小时、3小时转为毫秒
        // 30分钟 1800000
        // 2小时 7200000
        // 3小时 10800000
        // 48小时 172800000
        Integer timeOutInterval = carOnSiteEntity.getTimeOutInterval();
        Long timeDurationsTime = 0L;
        if (timeOutInterval == 30) {
            timeDurationsTime = 1800000L;
        } else if (timeOutInterval == 2) {
            timeDurationsTime = 7200000L;
        } else if (timeOutInterval == 3) {
            timeDurationsTime = 10800000L;
        }
        // 查询出指定的时间范围内的数据
        ArrayList<ParkOnSiteCar> parkShowOnSiteList = new ArrayList<>();
        for (ParkOnSiteCar parkOnSiteCar : parkOnSiteCars) {
            if (parkOnSiteCar.getParkingDurationTimes() <= 172800000L) {
                if (parkOnSiteCar.getParkingDurationTimes() >= timeDurationsTime) {
                    parkShowOnSiteList.add(parkOnSiteCar);
                }
            }
        }
        Comparator<ParkOnSiteCar> parkingDurationComparator = new Comparator<ParkOnSiteCar>() {
            @Override
            public int compare(ParkOnSiteCar o1, ParkOnSiteCar o2) {
                return Long.compare(o1.getParkingDurationTimes(), o2.getParkingDurationTimes());
            }
        };
        // 使用比较器对exportDataArrayList进行排序
        Collections.sort(parkShowOnSiteList, parkingDurationComparator);
        result.setMsg("查询成功！");
        result.setCode("0");
        result.setData(parkShowOnSiteList);
        return ResponseEntity.ok(result);
    }

    @ApiOperation("转换时间格式")
    public static String convertDateFormat(String input) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = null;
        try {
            date = inputFormat.parse(input);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return outputFormat.format(date);
    }

    @ApiOperation("转换时间格式")
    public static String convertDateFormatYY(String input) {
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = null;
        try {
            date = inputFormat.parse(input);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return outputFormat.format(date);
    }

    public static String enterTimeFromAdd(String dateString) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date parse = sdf.parse(dateString);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        calendar.add(Calendar.MONTH, -1); // 减去一个月
        calendar.add(Calendar.SECOND, 1); // 增加一秒
        String formattedDate = sdf.format(calendar.getTime()); // 转换为字符串格式
        return formattedDate;
    }

    @ApiOperation("查询单条停车记录详情")
    @GetMapping("/getParkDetail")
    public ResponseEntity getParkDetail(String parkCode, String carCode) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("parkCode", parkCode);
        params.put("carCode", carCode);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getParkDetail", params);
//        logger.info("调用aike接口后的查询单条停车记录详情" + data);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("预约车辆入场")
    @RequestMapping("/reportPreCarIn")
//    public ResponseEntity<AIKEResult> reportPreCarIn(@RequestBody JSONObject data) {
    public ResponseEntity<AIKEResult> reportPreCarIn(@RequestBody ReportPreCarIn data) {
        PreVipType preVipType = new PreVipType();
        logger.info("reportPreCarInData = " + data.getEnterTime());
//         先将数据存储到数据库，接着判断两次数据是否都正常操作
//         调用接口查询车场名称
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("parkCode", data.getParkCode());
        String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/getParkInfo", hashMap);
//        logger.info("preVipType数据库中添加数据中的查询车场名称：" + get);
        JSONObject jsonObject = JSONObject.parseObject(get);
        JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
        JSONObject jsonObjectDataData = (JSONObject) jsonObjectData.get("data");
        String parkName = (String) jsonObjectDataData.get("parkName");
        preVipType.setYardName(parkName);
        preVipType.setDeleted(0);
        preVipType.setPlateNumber(data.getEnterCarLicenseNumber());
        preVipType.setCreateTime(new Date());
        preVipType.setEnterTime(data.getEnterTime());
        preVipType.setReserveTime("测试使用");
        preVipType.setEnterChannelCustomCode(data.getEnterChannelCustomCode());
        preVipType.setPlateNumber(data.getEnterCarLicenseNumber());
        preVipType.setCreateTime(new Date());
        // 查询车牌号是否存在
        VehicleReservation vehicleReservation = vehicleReservationService.selectByCarName(data.getEnterCarLicenseNumber());
        Appointment appointment = appointmentService.getByQuery(data.getEnterCarLicenseNumber());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfOutput = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfInput = new SimpleDateFormat("yyyyMMddHHmmss");
        if (vehicleReservation != null) {
            //此处为预约过的车辆，在此处判断车辆的预进场信息是否是四季上东、6号岗入口
            if (!(data.getParkCode().equals("2KUG6XLU")
                    && data.getEnterChannelCustomCode().equals("PSWONBU2"))) {
                // 对于四季三期的1号岗的入口数据进行去除
                if (!(data.getParkCode().equals("2KUG6XLU")
                        && data.getEnterChannelCustomCode().equals("PSWONBU1"))) {
                    // 已经预约触发开闸
                    // 调用开关闸的接口参数
                    // 查询是否已经入场
                    HashMap<String, String> HASH_MAP_GATE = new HashMap<>();
                    HASH_MAP_GATE.put("parkCode", data.getParkCode());
                    // 此处的getEnterChannelCustomCode为通道自定义编码，且接口的value也必须为通道自定义编码
                    HASH_MAP_GATE.put("channelCode", data.getEnterChannelCustomCode());
                    HASH_MAP_GATE.put("opType", "0");
                    HASH_MAP_GATE.put("operator", "自动操作员");
                    String getGet = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/openGates", HASH_MAP_GATE);
                    logger.info(data.getEnterCarLicenseNumber() + "预约车辆Get = " + getGet + new Date());
                    vehicleReservation.setReserveTime(dateFormat.format(new Date()));
                    vehicleReservation.setReserveFlag(1);
                    Date formatDate = null;
                    try {
                        formatDate = sdfInput.parse(data.getEnterTime());
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    vehicleReservation.setEnterTime(sdfOutput.format(formatDate));
                    boolean num = vehicleReservationService.updateById(vehicleReservation);
                } else {
                    logger.info(data.getEnterCarLicenseNumber() + "四季三期地库1号岗数据！！");
                }
            } else {
                logger.info(data.getEnterCarLicenseNumber() + "四季三期地库6号岗数据！！");
            }
        } else {
            // TODO 判断在小程序预约的项目中添加的预约数据
            if (appointment != null) {
                HashMap<String, String> hashMapGate = new HashMap<>();
                hashMapGate.put("parkCode", data.getParkCode());
                hashMapGate.put("channelCode", data.getEnterChannelCustomCode());
                hashMapGate.put("opType", "0");
                hashMapGate.put("operator", "小程序开闸");
                String getGet = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/openGates", hashMapGate);
                logger.info(data.getEnterCarLicenseNumber() + "小程序开关闸：" + getGet);
                appointment.setVenuestatus("已入场");
                Date formatDate = null;
                try {
                    formatDate = sdfInput.parse(data.getEnterTime());
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                appointment.setArrivedate(sdfOutput.format(formatDate));
                boolean num = appointmentService.updateById(appointment);
                logger.info("小程序num = " + num);
            } else {
//                logger.info("走艾科系统");
            }
        }
        //创建空集合
        HashMap<Object, Object> hashEmptyMap = new HashMap<>();
        return ResponseEntity.ok(AIKEResult.success(hashEmptyMap));
    }

    /**
     * AKE进场上报
     *
     * @param data
     * @return
     * @throws ParseException
     */
    @RequestMapping("/reportCarIn")
// public ResponseEntity<AIKEResult> reportCarIn(@RequestBody JSONObject data) throws ParseException {
    public ResponseEntity<AIKEResult> reportCarIn(@RequestBody ReportCarInData data) throws ParseException {
        // 存储数据
        ReportCarIn reportCarIn = new ReportCarIn();
        if (data.getEnterCarLicenseNumber() != null && !data.getEnterCarLicenseNumber().contains("未识别")) {
            //添加文字
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = inputFormat.parse(data.getEnterTime());
            String outputEnterTime = outputFormat.format(date);
            reportCarIn.setEnterTime(outputEnterTime);
            reportCarIn.setEnterChannelId(String.valueOf(data.getEnterChannelId()));
            //调用艾科接口查询车场名称
            // 调用接口查询车场名称
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("parkCode", data.getParkCode());
            String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/getParkInfo", hashMap);
            JSONObject jsonObject = JSONObject.parseObject(get);
            JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
            JSONObject jsonObjectDataData = (JSONObject) jsonObjectData.get("data");
            String parkName = (String) jsonObjectDataData.get("parkName");
            reportCarIn.setYardName(parkName);
            reportCarIn.setDeleted(0);
            reportCarIn.setAreaId(String.valueOf(data.getAreaId()));
            // 查询小程序预约表中的数据是否存在车牌，存在的话则将进场时间赋值
            int update = appointmentService.updateByCarNumber(data.getEnterCarLicenseNumber(), outputEnterTime);
            if (update != 0) {
                logger.info("小程序更新成功条数：" + update);
            }
        }
        //创建空集合
        HashMap<Object, Object> hashEmptyMap = new HashMap<>();
        return ResponseEntity.ok(AIKEResult.success(hashEmptyMap));
    }
    @ApiOperation("预出场")
    @RequestMapping("/reportPreCarOut")
//    public ResponseEntity<AIKEResult> reportPreCarIn(@RequestBody JSONObject data) {
    public ResponseEntity<AIKEResult> reportPreCarOut(@RequestBody ReportPreCarOut data) {
        logger.info("预离场数据 = " + data.getEnterTime());
        // 查询车牌号是否存在
        VehicleReservation vehicleReservation = vehicleReservationService.selectByCarName(data.getLeaveCarLicenseNumber());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (vehicleReservation != null) {
            //此处为预约过的车辆，判断是否是太平桥百盛
            if (data.getParkCode().equals("2KUIN1CF")) {
                    // 已经预约触发开闸
                    // 调用开关闸的接口参数
                    // 查询是否已经入场
                    HashMap<String, String> HASH_MAP_GATE = new HashMap<>();
                    HASH_MAP_GATE.put("parkCode", data.getParkCode());
                    // 此处的getEnterChannelCustomCode为通道自定义编码，且接口的value也必须为通道自定义编码
                    HASH_MAP_GATE.put("channelCode", data.getLeaveChannelCustomCode());
                    HASH_MAP_GATE.put("opType", "0");
                    HASH_MAP_GATE.put("operator", "自动操作员");
                    String getGet = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/openGates", HASH_MAP_GATE);
                    logger.info(data.getLeaveCarLicenseNumber() + "离场车辆Get = " + getGet + new Date());
            } else if (data.getParkCode().equals("76AJJXOOM")) {
                // 中央大街人和地下预约车辆
                // 已经预约触发开闸
                // 调用开关闸的接口参数
                // 查询是否已经入场
                HashMap<String, String> HASH_MAP_GATE = new HashMap<>();
                HASH_MAP_GATE.put("parkCode", data.getParkCode());
                // 此处的getEnterChannelCustomCode为通道自定义编码，且接口的value也必须为通道自定义编码
                HASH_MAP_GATE.put("channelCode", data.getLeaveChannelCustomCode());
                HASH_MAP_GATE.put("opType", "0");
                HASH_MAP_GATE.put("operator", "自动操作员");
                String getGet = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/openGates", HASH_MAP_GATE);
                logger.info(data.getLeaveCarLicenseNumber() + "离场车辆Get = " + getGet + new Date());
            } else {
                logger.info(data.getLeaveCarLicenseNumber() + "非太平桥百盛、中央大街人和地下预约车辆！！");
            }
        }else {
            // 没有预约的车辆，直接开闸
            logger.info(data.getLeaveCarLicenseNumber() + "未预约车辆！！");
        }
        //创建空集合
        HashMap<Object, Object> hashEmptyMap = new HashMap<>();
        return ResponseEntity.ok(AIKEResult.success(hashEmptyMap));
    }

    @RequestMapping("/reportCarOut")
//    public ResponseEntity<AIKEResult> reportCarOut(@RequestBody JSONObject data) throws ParseException {
    public ResponseEntity<AIKEResult> reportCarOut(@RequestBody ReportCarOutData data) throws ParseException {
//        logger.info("reportCarOut_data = " + data.getLeaveTime());
        if (data.getEnterTime() != null) {
            //格式化时间格式
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = inputFormat.parse(data.getEnterTime());
            String outputEnterTime = outputFormat.format(date);
            SimpleDateFormat inputFormatLeave = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat outputFormatLeave = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date dateLeave = inputFormatLeave.parse(data.getLeaveTime());
            String outputLeaveTime = outputFormatLeave.format(dateLeave);
//            // 入场不添加离场时间
//            SimpleDateFormat inputFormatOperator = new SimpleDateFormat("yyyyMMddHHmmss");
//            SimpleDateFormat outputFormatOperator = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date dateOperator = inputFormatOperator.parse(data.getInOperatorTime());
//            String outputOperatorTime = outputFormatOperator.format(dateOperator);
            // 调用接口查询车场名称
//            HashMap<String, String> hashMap = new HashMap<>();
//            hashMap.put("parkCode", data.getParkCode());
//            String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/getParkInfo", hashMap);
//            JSONObject jsonObject = JSONObject.parseObject(get);
//            JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
//            JSONObject jsonObjectDataData = (JSONObject) jsonObjectData.get("data");
//            String parkName = (String) jsonObjectDataData.get("parkName");
            int update = appointmentService.updateLeaveTimeByCarNumber(data.getEnterCarLicenseNumber(), outputEnterTime, outputLeaveTime);
            if (update != 0) {
                logger.info("小程序离场时间修改条数为：", update);
            }
        }
        HashMap<Object, Object> hashEmptyMap = new HashMap<>();
        return ResponseEntity.ok(AIKEResult.success(hashEmptyMap));
    }
}