package com.parkingmanage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.parkingmanage.common.Result;
import com.parkingmanage.entity.Violations;
import com.parkingmanage.service.ViolationsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 违规记录管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@RestController
@RequestMapping("/parking/violations")
@Api(tags = "违规记录管理")
public class ViolationsController {

    @Resource
    private ViolationsService violationsService;

    @PostMapping
    @ApiOperation("创建违规记录")
    public Result<Boolean> createViolation(@RequestBody Violations violation) {
        boolean result = violationsService.createViolation(violation);
        return result ? Result.success(true) : Result.error("创建失败");
    }

    @GetMapping
    @ApiOperation("分页查询违规记录")
    public Result<IPage<Map<String, Object>>> getViolations(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(defaultValue = "20") Integer size,
            @ApiParam("车牌号") @RequestParam(required = false) String plateNumber,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("违规类型") @RequestParam(required = false) String violationType,
            @ApiParam("开始时间") @RequestParam(required = false) String startDate,
            @ApiParam("结束时间") @RequestParam(required = false) String endDate,
            @ApiParam("车主ID") @RequestParam(required = false) Integer ownerId) {

        // 解析日期参数，支持多种格式
        LocalDateTime parsedStartDate = parseDateTime(startDate, true);
        LocalDateTime parsedEndDate = parseDateTime(endDate, false);

        Page<Map<String, Object>> pageParam = new Page<>(page, size);
        IPage<Map<String, Object>> result = violationsService.getViolationsWithOwnerInfo(
                pageParam, plateNumber, status, violationType, parsedStartDate, parsedEndDate, ownerId);

        return Result.success(result);
    }

    @PutMapping("/{id}/status")
    @ApiOperation("更新违规记录状态")
    public Result<Boolean> updateViolationStatus(
            @ApiParam("违规记录ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam String status,
            @ApiParam("处理备注") @RequestParam(required = false) String remark,
            @ApiParam("处理人ID") @RequestParam(required = false) Integer handlerId) {
        
        boolean result = violationsService.updateViolationStatus(id, status, remark, handlerId);
        return result ? Result.success(true) : Result.error("更新失败");
    }

    @GetMapping("/statistics")
    @ApiOperation("获取违规统计数据")
    public Result<Map<String, Object>> getStatistics(
            @ApiParam("开始时间") @RequestParam(required = false) String startDate,
            @ApiParam("结束时间") @RequestParam(required = false) String endDate,
            @ApiParam("车牌号") @RequestParam(required = false) String plateNumber) {

        // 解析日期参数
        LocalDateTime parsedStartDate = parseDateTime(startDate, true);
        LocalDateTime parsedEndDate = parseDateTime(endDate, false);

        Map<String, Object> result = violationsService.getViolationStatistics(parsedStartDate, parsedEndDate, plateNumber);
        return Result.success(result);
    }

    @GetMapping("/high-risk-vehicles")
    @ApiOperation("获取高风险车辆列表")
    public Result<List<Map<String, Object>>> getHighRiskVehicles(
            @ApiParam("开始时间") @RequestParam(required = false) String startDate,
            @ApiParam("结束时间") @RequestParam(required = false) String endDate,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit) {

        // 解析日期参数
        LocalDateTime parsedStartDate = parseDateTime(startDate, true);
        LocalDateTime parsedEndDate = parseDateTime(endDate, false);

        List<Map<String, Object>> result = violationsService.getHighRiskVehicles(parsedStartDate, parsedEndDate, limit);
        return Result.success(result);
    }

    @GetMapping("/owners/by-plate/{plateNumber}")
    @ApiOperation("根据车牌号查询车主信息")
    public Result<Map<String, Object>> getOwnerByPlateNumber(
            @ApiParam("车牌号") @PathVariable String plateNumber) {
        Map<String, Object> result = violationsService.getOwnerByPlateNumber(plateNumber);
        return result != null ? Result.success(result) : Result.error("车主信息不存在");
    }

    @GetMapping("/owners/plate-suggestions")
    @ApiOperation("车牌号搜索建议")
    public Result<List<Map<String, Object>>> getPlateSuggestions(
            @ApiParam("关键词") @RequestParam String keyword) {
        
        List<Map<String, Object>> result = violationsService.getPlateSuggestions(keyword);
        return Result.success(result);
    }

    @GetMapping("/owners/{ownerId}/vehicles")
    @ApiOperation("获取车主的车辆列表")
    public Result<List<Map<String, Object>>> getOwnerVehicles(
            @ApiParam("车主ID") @PathVariable Integer ownerId) {
        
        List<Map<String, Object>> result = violationsService.getOwnerVehicles(ownerId);
        return Result.success(result);
    }

    @PutMapping("/owners/{ownerId}/credit-score")
    @ApiOperation("更新车主信用分")
    public Result<Boolean> updateOwnerCreditScore(
            @ApiParam("车主ID") @PathVariable Integer ownerId,
            @ApiParam("信用分") @RequestParam Integer creditScore) {
        
        boolean result = violationsService.updateOwnerCreditScore(ownerId, creditScore);
        return result ? Result.success(true) : Result.error("更新失败");
    }

    /**
     * 解析日期时间字符串，支持多种格式
     * @param dateStr 日期字符串
     * @param isStartDate 是否为开始日期（开始日期设为00:00:00，结束日期设为23:59:59）
     * @return LocalDateTime对象，如果解析失败返回null
     */
    private LocalDateTime parseDateTime(String dateStr, boolean isStartDate) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析完整的日期时间格式 yyyy-MM-dd HH:mm:ss
            if (dateStr.length() > 10) {
                return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            // 解析只有日期的格式 yyyy-MM-dd
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 如果是开始日期，设置为当天的00:00:00
            if (isStartDate) {
                return date.atStartOfDay();
            } else {
                // 如果是结束日期，设置为当天的23:59:59
                return date.atTime(23, 59, 59);
            }

        } catch (Exception e) {
            // 如果解析失败，记录日志并返回null
            System.err.println("日期解析失败: " + dateStr + ", 错误: " + e.getMessage());
            return null;
        }
    }
}
