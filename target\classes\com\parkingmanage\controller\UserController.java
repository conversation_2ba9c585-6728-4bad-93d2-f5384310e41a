package com.parkingmanage.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.parkingmanage.common.Result;
import com.parkingmanage.entity.Department;
import com.parkingmanage.entity.Role;
import com.parkingmanage.entity.User;
import com.parkingmanage.service.DepartmentService;
import com.parkingmanage.service.RoleService;
import com.parkingmanage.service.UserService;
import com.parkingmanage.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jdk.nashorn.internal.objects.annotations.Optimistic;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.implementation.bind.annotation.Origin;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 <p>
 前端控制器
 </p>

 <AUTHOR>
 * @since 2022-02-27
 */
@Slf4j
@RestController
@RequestMapping("/parking/user")
@CrossOrigin
@Api(tags = "用户管理")
public class UserController {
    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;
    @Resource
    private DepartmentService departmentService;

    @ApiOperation("新增用户")
    @PostMapping
    public ResponseEntity<Result> saveUser(@RequestBody User user) {
        userService.save(user);
        return ResponseEntity.ok(new Result());
    }

    @ApiOperation("修改")
    @PutMapping
    public ResponseEntity<Result> updateUser(@RequestBody User user) {
        userService.updateById(user);
        return ResponseEntity.ok(new Result());
    }

    @ApiOperation("删除")
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable Integer id) {
        return userService.removeById(id);
    }

    @ApiOperation("查询单条")
    @GetMapping("/{id}")
    public User findById(@PathVariable Integer id) {
        return userService.getById(id);
    }

    @ApiOperation("查询所有")
    @GetMapping("/listAll")
    public List<User> findAll() {
        return userService.list();
    }

    @ApiOperation("登录")
    @GetMapping("/login")
    public ResponseEntity<Result> login(@RequestParam(value = "username") String loginName,
                                        @RequestParam(value = "password") String password) {
        User user = userService.login(loginName, password);
        System.out.println(loginName);
        System.out.println(password);
        return ResponseEntity.ok(Result.success(user));
    }

    @ApiOperation("按照不同条件分页查询")
    @GetMapping("/page")
    public ResponseEntity<Result> getUser(@RequestParam(required = false,value = "userName") String userName,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        List<User> users = userService.list(Wrappers.<User>lambdaQuery().eq(StringUtils.hasLength(userName), User::getUserName, userName));
        for (User user : users) {
            Department department = departmentService.getById(user.getDepartmentId());
            Role role = roleService.getById(user.getRoleId());
            if (ObjectUtils.isNotEmpty(department)) {
                user.setDepartmentName(department.getDepartmentName());
            }
            if (ObjectUtils.isNotEmpty(role)) {
                user.setRoleName(role.getName());
            }
        }
//        System.out.println("users = " + users);
        return ResponseEntity.ok(Result.success(PageUtils.getPage(users, pageNum, pageSize)));

    }

}

