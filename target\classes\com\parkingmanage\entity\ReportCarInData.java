package com.parkingmanage.entity;

import lombok.Data;

import java.util.List;

/**
 * @program: ParkManage
 * @description:
 * @author: lzx
 * @create: 2024-04-26 08:50
 **/
@Data
public class ReportCarInData {
//    private int enterVipType;
//    private String enterChannelCode;
//    private int enterSpeed;
//    private String inOperatorTime;
//    private String enterCarLicenseNumber;
//    private String enterStatisticalArea;
//    private String inOperatorName;
//    private String enterCustomVipName;
//    private String remark;
//    private int enterChannelId;
//    private int enterCarLicenseColor;
//    private int enterCarColor;
//    private String fromAreaId;
//    private String lockKey;
//    private int correctType;
//    private String enterTime;
//    private String areaId;
//    private int enterNovipCode;
//    private String toAreaId;
//    private String enterChannelCustomCode;
//    private List<EnterImageArray> enterImageArray;
//    private int recordType;
//    private int confidence;
//    private int enterCarType;
//    private String enterChannelName;
//    private String carLicenseNumber;
//    private int enterType;
//    private String lastCorrectName;
//    private ExtendInfoIn extendInfo;
//    private int openGateTime;
//    private String parkingCode;
//    private String enterChargeGroupCode;
//    private String enterCarCardNumber;
//    private int enterRecognitionConfidence;
//    private String lastCorrectLicenseNumber;
//    private int correctConfidence;
//    private String cmd;
//    private String parkCode;
//    private int enterCarLicenseType;
//    private String enterCustomVipId;
//    private int enterCarLogo;
//    private String enterVipRule;
//    private int isCorrect;
    private int enterVipType;
    private String enterChannelCode;
    private int isParkInOut;
    private int enterSpeed;
    private String inOperatorTime;
    private String enterCarLicenseNumber;
    private String enterStatisticalArea;
    private String inOperatorName;
    private String enterCustomVipName;
    private String remark;
    private int enterChannelId;
    private int enterCarLicenseColor;
    private int enterCarColor;
    private String fromAreaId;
    private String lockKey;
    private int correctType;
    private String enterTime;
    private int enterNovipCode;
    private String toAreaId;
    private String enterChannelCustomCode;
    private List<EnterImageArray> enterImageArray;
    private int recordType;
    private int confidence;
    private int enterCarType;
    private String enterChannelName;
    private String carLicenseNumber;
    private int enterType;
    private String lastCorrectName;
    private ExtendInfoIn extendInfo;
    private int openGateTime;
    private String parkingCode;
    private String enterChargeGroupCode;
    private String enterCarCardNumber;
    private int enterRecognitionConfidence;
    private long areaId;
    private String lastCorrectLicenseNumber;
    private int correctConfidence;
    private String cmd;
    private String parkCode;
    private int enterCarLicenseType;
    private String enterCustomVipId;
    private int enterCarLogo;
    private String enterVipRule;
    private int isCorrect;
}
