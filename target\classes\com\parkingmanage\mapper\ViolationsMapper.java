package com.parkingmanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.parkingmanage.entity.Violations;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 违规记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface ViolationsMapper extends BaseMapper<Violations> {

    /**
     * 分页查询违规记录
     */
    IPage<Map<String, Object>> selectViolationsWithOwnerInfo(
            Page<Map<String, Object>> page,
            @Param("plateNumber") String plateNumber,
            @Param("status") String status,
            @Param("violationType") String violationType,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("ownerId") Integer ownerId
    );

    /**
     * 获取高风险车辆列表
     */
    List<Map<String, Object>> selectHighRiskVehicles(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("limit") Integer limit
    );

    /**
     * 获取违规统计数据
     */
    List<Map<String, Object>> selectViolationStatistics(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("plateNumber") String plateNumber
    );

    /**
     * 根据车牌号查询车主ID
     */
    Integer selectOwnerIdByPlateNumber(@Param("plateNumber") String plateNumber);

    /**
     * 更新车主信用分
     */
    int updateOwnerCreditScore(@Param("ownerId") Integer ownerId, @Param("deduction") Integer deduction);

    /**
     * 按日期统计违规数量
     */
    List<Map<String, Object>> selectDailyViolationStats(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("plateNumber") String plateNumber
    );

    /**
     * 按违规类型统计
     */
    List<Map<String, Object>> selectViolationTypeStats(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );
}
