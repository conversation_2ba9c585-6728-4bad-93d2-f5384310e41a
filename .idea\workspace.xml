<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="612af413-530f-4424-b602-dc9e2c85de70" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\maven\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\maven\apache-maven-3.9.9\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2vcIuiX30D99GshoQXBSLuKX4pu" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.parkingmanage [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.parkingmanage [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.parkingmanage [package].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;Database detector&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ParkingManageApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/PakingDemo/parking-demo/src/main/resources&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\PakingDemo\parking-demo\src\main\resources" />
      <recent name="D:\PakingDemo\parking-demo\src\main\java\com\parkingmanage\mapper\xml" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="ParkingManageApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="parkingmanage" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.parkingmanage.ParkingManageApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.parkingmanage.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ParkingManageApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="612af413-530f-4424-b602-dc9e2c85de70" name="Changes" comment="" />
      <created>1744438159873</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744438159873</updated>
      <workItem from="1744438160959" duration="3012000" />
      <workItem from="1744442447873" duration="167000" />
      <workItem from="1749254656692" duration="11777000" />
      <workItem from="1749447329697" duration="5365000" />
      <workItem from="1749542455620" duration="2632000" />
      <workItem from="1749604754501" duration="7666000" />
      <workItem from="1749620649738" duration="11240000" />
      <workItem from="1749686265421" duration="9386000" />
      <workItem from="1749706441037" duration="8720000" />
      <workItem from="1749772460164" duration="10948000" />
      <workItem from="1749858926890" duration="9606000" />
      <workItem from="1750037896214" duration="11425000" />
      <workItem from="1750119366865" duration="24000" />
      <workItem from="1750119412280" duration="1607000" />
      <workItem from="1750121135379" duration="1929000" />
      <workItem from="1750140309318" duration="4809000" />
      <workItem from="1750746233277" duration="8738000" />
      <workItem from="1750809567209" duration="16536000" />
      <workItem from="1750841643554" duration="655000" />
      <workItem from="1750896224676" duration="16352000" />
      <workItem from="1750982691101" duration="19094000" />
      <workItem from="1751068513493" duration="17717000" />
      <workItem from="1751242481096" duration="17698000" />
      <workItem from="1751328314643" duration="11604000" />
      <workItem from="1751358884766" duration="2961000" />
      <workItem from="1751414580065" duration="14507000" />
      <workItem from="1751500388751" duration="15590000" />
      <workItem from="1751587534005" duration="8718000" />
      <workItem from="1751614545091" duration="2111000" />
      <workItem from="1751621504888" duration="5000" />
      <workItem from="1751673036811" duration="103000" />
      <workItem from="1751673145885" duration="18124000" />
      <workItem from="1751846478942" duration="115000" />
      <workItem from="1751846599939" duration="112000" />
      <workItem from="1751847336890" duration="12801000" />
      <workItem from="1751932944897" duration="8919000" />
      <workItem from="1752020674567" duration="6657000" />
      <workItem from="1752107033857" duration="5300000" />
      <workItem from="1752129155305" duration="3068000" />
      <workItem from="1752192101684" duration="15624000" />
      <workItem from="1752278406313" duration="12411000" />
      <workItem from="1752451086914" duration="20438000" />
      <workItem from="1752537601365" duration="12311000" />
      <workItem from="1752624464704" duration="7798000" />
      <workItem from="1752710165587" duration="8039000" />
      <workItem from="1752796819247" duration="9559000" />
      <workItem from="1752883657616" duration="8877000" />
      <workItem from="1752907182557" duration="3292000" />
      <workItem from="1753055529502" duration="14088000" />
      <workItem from="1753141951614" duration="10445000" />
      <workItem from="1753228847159" duration="7100000" />
      <workItem from="1753249892315" duration="1901000" />
      <workItem from="1753254999794" duration="1787000" />
      <workItem from="1753262402287" duration="604000" />
      <workItem from="1753315298398" duration="17332000" />
      <workItem from="1753401839937" duration="4947000" />
      <workItem from="1753512726712" duration="5519000" />
      <workItem from="1753660506106" duration="11827000" />
      <workItem from="1753747697158" duration="11861000" />
      <workItem from="1753840883932" duration="7503000" />
      <workItem from="1753939921874" duration="6958000" />
      <workItem from="1754008297793" duration="876000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>