package com.parkingmanage.service;

import com.parkingmanage.entity.BlackList;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
public interface BlackListService extends IService<BlackList> {

    List<BlackList> queryInfoOnly(String parkName, String carCode, String specialCarTypeConfigName, String userName, String blackReason, String remark1, String remark2);

    List<BlackList> findOne(BlackList blackList);

}
