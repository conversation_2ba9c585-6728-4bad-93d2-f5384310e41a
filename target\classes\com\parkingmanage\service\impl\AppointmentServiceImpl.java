package com.parkingmanage.service.impl;

import com.parkingmanage.entity.Appointment;
import com.parkingmanage.mapper.AppointmentMapper;
import com.parkingmanage.query.VehicleQuery;
import com.parkingmanage.service.AppointmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
@Service
public class AppointmentServiceImpl extends ServiceImpl<AppointmentMapper, Appointment> implements AppointmentService {
    @Override
    public Appointment getAppointmentByOrderNumber(String orderNumber){
        return baseMapper.selectAppointmentByOrderNumber(orderNumber);
    }
    @Override
    public  List<Appointment> listAppointNoAudit(String community,String ownername,String  recorddate){
        return baseMapper.listAppointNoAudit(community,ownername,recorddate);
    }

    @Override
    public Appointment getByQuery(String enterCarLicenseNumber) {
        return baseMapper.getByQuery(enterCarLicenseNumber);
    }

    @Override
    public int updateByCarNumber(String enterCarLicenseNumber, String enterTime) {
        return baseMapper.updateByCarNumber(enterCarLicenseNumber,enterTime);
    }

    @Override
    public int updateLeaveTimeByCarNumber(String enterCarLicenseNumber, String enterTime, String leaveTime) {
        return baseMapper.updateLeaveTimeByCarNumber(enterCarLicenseNumber,enterTime,leaveTime);
    }

    @Override
    public List<Appointment> getAppointmentPlateNumber(String plateNumber) {
        return baseMapper.getAppointmentPlateNumber(plateNumber);
    }

    @Override
    public List<Appointment> subAppointQueryListDuration(String openid, String platenumber, String visitorphone, String visitdateBegin, String recorddateBegin, String visitdateEnd, String recorddateEnd) {
        return baseMapper.subAppointQueryListDuration(openid,platenumber,visitdateBegin,visitdateEnd,recorddateBegin,recorddateEnd,visitorphone);
    }

    @Override
    public List<Appointment> visitorList(String openid){
        return baseMapper.visitorList(openid);
    }
    @Override
    public List<Appointment> managerList(String openid){
        return baseMapper.managerList(openid);
    }
    @Override
    public List<Appointment> vehicleQueryList(String openid,String platenumber,String leavedate){
        return baseMapper.vehicleQueryList(openid,platenumber,leavedate);
    }
    @Override
    public List<Appointment> subAppointQueryList(String openid, String platenumber, String visitorphone, String visitdateBegin, String visitdateEnd, String recorddateBegin,String recorddateEnd){
        return baseMapper.subAppointQueryList(openid,platenumber,visitorphone,visitdateBegin,visitdateEnd,recorddateBegin,recorddateEnd);
    }
    @Override
    public List<Appointment> auditQueryList(String openid, String platenumber, String visitorphone, String visitdateBegin,  String visitdateEnd,String recorddateBegin, String recorddateEnd){
        return baseMapper.auditQueryList(openid,platenumber,visitorphone,visitdateBegin,visitdateEnd,recorddateBegin,recorddateEnd);
    }
    @Override
    public  List<Appointment> allpage(String community,String plateNumber,String  visitdate,String auditstatus){
        return baseMapper.allpage(community,plateNumber,visitdate,auditstatus);
    }
    @Override
    public  List<Appointment> venuepage(String community,String plateNumber,String  arrivedate,String leavedate,String venuestatus){
        return baseMapper.venuepage(community,plateNumber,arrivedate,leavedate,venuestatus);
    }
    
    @Override
    public List<Appointment> listByPhone(String phone) {
        return baseMapper.listByPhone(phone);
    }

    @Override
    public List<Appointment> listByAddress(String community, String building, String units, String floor, String room) {
        return baseMapper.listByAddress(community, building, units, floor, room);
    }

}
