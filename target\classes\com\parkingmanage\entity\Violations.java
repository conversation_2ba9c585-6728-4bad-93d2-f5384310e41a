package com.parkingmanage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 违规记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("violations")
@ApiModel(value="Violations对象", description="违规记录表")
public class Violations implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "车主ID")
    private Integer ownerId;

    @ApiModelProperty(value = "违规类型")
    private String violationType;

    @ApiModelProperty(value = "自定义违规类型")
    private String customType;

    @ApiModelProperty(value = "违规位置")
    private String location;

    @ApiModelProperty(value = "违规描述")
    private String description;

    @ApiModelProperty(value = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime appointmentTime;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enterTime;

    @ApiModelProperty(value = "离场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leaveTime;

    @ApiModelProperty(value = "处理状态")
    private String status;

    @ApiModelProperty(value = "严重程度")
    private String severity;

    @ApiModelProperty(value = "举报人ID")
    private Integer reporterId;

    @ApiModelProperty(value = "处理人ID")
    private Integer handlerId;

    @ApiModelProperty(value = "现场照片")
    private String photos;

    @ApiModelProperty(value = "语音备注文件路径")
    private String voiceMemo;

    @ApiModelProperty(value = "处理备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
