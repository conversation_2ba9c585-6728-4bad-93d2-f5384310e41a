<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.CustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Customer">
        <id column="customer_id" property="customerId" />
        <result column="customer_code" property="customerCode" />
        <result column="customer_name" property="customerName" />
        <result column="leader" property="leader" />
        <result column="telephone" property="telephone" />
        <result column="registion_date" property="registionDate" />
        <result column="remarks" property="remarks" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        customer_id, customer_code, customer_name, leader, telephone, registion_date, remarks, deleted
    </sql>

</mapper>
