package com.parkingmanage.controller;


import com.parkingmanage.common.Result;
import com.parkingmanage.entity.Area;
import com.parkingmanage.entity.Butler;
import com.parkingmanage.entity.Community;
import com.parkingmanage.entity.Department;
import com.parkingmanage.query.TransmitAreaQuery;
import com.parkingmanage.service.AreaService;
import com.parkingmanage.service.ButlerService;
import com.parkingmanage.vo.AreaResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-08
 */
@RestController
@RequestMapping("/parking/area")
public class AreaController {
    @Resource
    private ButlerService butlerService;
    @Resource
    private AreaService areaService;

    @ApiOperation("添加")
    @PostMapping("/insertArea")
    public ResponseEntity<Result> insertArea(@RequestParam(required = false) String province,
                                             @RequestParam(required = false) String city,
                                             @RequestParam(required = false) String district,
                                             @RequestParam(required = false) String community,
                                             @RequestParam(required = false) String usercode,
                                             @RequestParam(required = false) String username,
                                             @RequestParam("arrayId[]") List<Integer> arrayId) {
        Butler butler = butlerService.getByUsercode(province, city, district, community, usercode);
        String openid = butler.getOpenid();
        areaService.deleteArea(province, city, district, community, usercode);
        areaService.insertArea(usercode, username, openid, arrayId);
        return ResponseEntity.ok(new Result());
    }
    @ApiOperation("添加")
    @PostMapping("/batsave")
    public ResponseEntity<Result> batSave(@RequestBody List<Community> communitys) {
        System.out.println(communitys);
        Area area = new Area();
        if (!communitys.isEmpty()) {
            String openid = communitys.get(0).getOpenid();
            areaService.deleteByOpenid(openid);
            Iterator<Community> iter = communitys.iterator();
            while (iter.hasNext()) {
                Community community = iter.next();
                area.setProvince(community.getProvince());
                area.setCity(community.getCity());
                area.setDistrict(community.getDistrict());
                area.setCommunity(community.getCommunity());
                area.setBuilding(community.getBuilding());
                area.setUnits(community.getUnits().toString());
                area.setFloor(community.getFloor().toString());
                area.setOpenid(community.getOpenid());
                area.setUsername(community.getUsername());
                areaService.save(area);
            }
        }
        return ResponseEntity.ok(new Result());
    }
    @ApiOperation("查询单条")
    @GetMapping("/{openid}")
    public Area getByOpenId(@PathVariable String openid) {
        Area area = areaService.getByOpenId(openid);
        return area;
    }

    @ApiOperation("查询单条")
    @GetMapping("/getAreaByOpenId/{openid}")
    public List<AreaResult> getAreaByOpenId(@PathVariable String openid) {
        List<AreaResult> areaResult = areaService.getAreaByOpenId(openid);
        return areaResult;
    }

    @ApiOperation("查询单条")
    @GetMapping("/transmit")
    @ResponseBody
    public List<Area> getTransmitByOpenId(TransmitAreaQuery transmitAreaQuery) {
//        System.out.println("000000000000000000000000000");
//        System.out.println(transmitAreaQuery);
        Area area = areaService.getByOpenId(transmitAreaQuery.getOpenid());
        transmitAreaQuery.setProvince(area.getProvince());
        transmitAreaQuery.setCity(area.getCity());
        transmitAreaQuery.setDistrict(area.getDistrict());
        transmitAreaQuery.setCommunity(area.getCommunity());
        return areaService.getTransmitByOpenId(transmitAreaQuery);
    }

    @ApiOperation("查询单条")
    @GetMapping("/getparking")
    public Area getParkingInfo(String province, String city, String district, String community, String building) {
//        System.out.println("99999999999900000000000000000");
//        System.out.println(community);
        return areaService.getParkingInfo(province, city, district, community, building);
    }
//    public R<Map<String,Object>> getMemberInfoByOpenId(@PathVariable String openid) {
//        Area area=areaService.getMemberInfoByOpenId(openid);
//        Map<String,Object>  wxMap=new HashMap<>();
//        if (area == null) {
//            wxMap.put("count",0);
//            wxMap.put("data",area);
//        }else {
//            wxMap.put("count",1);
//            wxMap.put("data",area);
//        }
//
//        HashMap<String, Object> dataMap = new HashMap<>();
//        dataMap.put("data",wxMap);
//        return R.ok(dataMap);
//
//    }
}

