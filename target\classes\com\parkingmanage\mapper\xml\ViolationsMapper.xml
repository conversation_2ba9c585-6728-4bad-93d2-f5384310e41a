<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.ViolationsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Violations">
        <id column="id" property="id" />
        <result column="plate_number" property="plateNumber" />
        <result column="owner_id" property="ownerId" />
        <result column="violation_type" property="violationType" />
        <result column="custom_type" property="customType" />
        <result column="location" property="location" />
        <result column="description" property="description" />
        <result column="appointment_time" property="appointmentTime" />
        <result column="enter_time" property="enterTime" />
        <result column="leave_time" property="leaveTime" />
        <result column="status" property="status" />
        <result column="severity" property="severity" />
        <result column="reporter_id" property="reporterId" />
        <result column="handler_id" property="handlerId" />
        <result column="photos" property="photos" />
        <result column="voice_memo" property="voiceMemo" />
        <result column="remark" property="remark" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 分页查询违规记录 -->
    <select id="selectViolationsWithOwnerInfo" resultType="map">
        SELECT 
            v.id,
            v.plate_number as plateNumber,
            v.violation_type as violationType,
            v.custom_type as customType,
            v.location,
            v.description,
            v.appointment_time as appointmentTime,
            v.enter_time as enterTime,
            v.leave_time as leaveTime,
            v.status,
            CASE 
                WHEN v.status = 'pending' THEN '待处理'
                WHEN v.status = 'processing' THEN '处理中'
                WHEN v.status = 'completed' THEN '已处理'
                WHEN v.status = 'cancelled' THEN '已取消'
            END as statusText,
            v.severity,
            v.photos,
            v.voice_memo as voiceMemo,
            v.remark,
            v.created_at as createdAt,
            o.ownername as ownerName,
            o.ownerphone as ownerPhone,
            CONCAT(o.building, '栋', o.units, '单元', o.roomnumber, '室') as ownerAddress,
            o.credit_score as creditScore,
            CASE 
                WHEN LENGTH(v.plate_number) = 8 THEN true 
                ELSE false 
            END as isNewEnergy
        FROM violations v
        LEFT JOIN ownerinfo o ON v.owner_id = o.id
        <where>
            <if test="plateNumber != null and plateNumber != ''">
                AND v.plate_number LIKE CONCAT('%', #{plateNumber}, '%')
            </if>
            <if test="status != null and status != ''">
                AND v.status = #{status}
            </if>
            <if test="violationType != null and violationType != ''">
                AND v.violation_type = #{violationType}
            </if>
            <if test="startDate != null">
                AND v.created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.created_at &lt;= #{endDate}
            </if>
            <if test="ownerId != null">
                AND v.owner_id = #{ownerId}
            </if>
        </where>
        ORDER BY v.created_at DESC
    </select>

    <!-- 获取高风险车辆列表 -->
    <select id="selectHighRiskVehicles" resultType="map">
        SELECT 
            v.plate_number as plateNumber,
            COUNT(*) as count,
            o.ownername as ownerName,
            o.ownerphone as phone,
            CONCAT(o.building, '栋', o.units, '单元', o.roomnumber, '室') as address,
            o.credit_score as creditScore,
            CASE 
                WHEN LENGTH(v.plate_number) = 8 THEN true 
                ELSE false 
            END as isNewEnergy,
            GROUP_CONCAT(DISTINCT v.violation_type) as violationTypes,
            MAX(v.created_at) as lastViolationTime
        FROM violations v
        LEFT JOIN ownerinfo o ON v.owner_id = o.id
        <where>
            <if test="startDate != null">
                AND v.created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.created_at &lt;= #{endDate}
            </if>
        </where>
        GROUP BY v.plate_number
        HAVING count >= 2
        ORDER BY count DESC, lastViolationTime DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取违规统计数据 -->
    <select id="selectViolationStatistics" resultType="map">
        SELECT 
            v.status,
            COUNT(*) as count
        FROM violations v
        <where>
            <if test="startDate != null">
                AND v.created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.created_at &lt;= #{endDate}
            </if>
            <if test="plateNumber != null and plateNumber != ''">
                AND v.plate_number = #{plateNumber}
            </if>
        </where>
        GROUP BY v.status
    </select>

    <!-- 根据车牌号查询车主ID -->
    <select id="selectOwnerIdByPlateNumber" resultType="java.lang.Integer">
        SELECT o.id FROM ownerinfo o
        WHERE FIND_IN_SET(#{plateNumber}, REPLACE(o.plates, ' ', '')) > 0
        AND o.isaudit = '是'
        LIMIT 1
    </select>

    <!-- 更新车主信用分 -->
    <update id="updateOwnerCreditScore">
        UPDATE ownerinfo 
        SET credit_score = GREATEST(0, credit_score - #{deduction})
        WHERE id = #{ownerId}
    </update>

    <!-- 按日期统计违规数量 -->
    <select id="selectDailyViolationStats" resultType="map">
        SELECT 
            DATE(v.created_at) as date,
            COUNT(*) as count
        FROM violations v
        <where>
            <if test="startDate != null">
                AND v.created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.created_at &lt;= #{endDate}
            </if>
            <if test="plateNumber != null and plateNumber != ''">
                AND v.plate_number = #{plateNumber}
            </if>
        </where>
        GROUP BY DATE(v.created_at)
        ORDER BY date
    </select>

    <!-- 按违规类型统计 -->
    <select id="selectViolationTypeStats" resultType="map">
        SELECT 
            v.violation_type as violationType,
            COUNT(*) as count
        FROM violations v
        <where>
            <if test="startDate != null">
                AND v.created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND v.created_at &lt;= #{endDate}
            </if>
        </where>
        GROUP BY v.violation_type
        ORDER BY count DESC
    </select>

</mapper>
