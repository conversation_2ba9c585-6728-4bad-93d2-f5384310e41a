<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.PreVipTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.PreVipType">
        <id column="id" property="id" />
        <result column="yard_name" property="yardName" />
        <result column="plate_number" property="plateNumber" />
        <result column="leave_time" property="leaveTime" />
        <result column="enter_pre_vip_type" property="enterPreVipType" />
        <result column="enter_time" property="enterTime" />
        <result column="reserve_time" property="reserveTime" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, yard_name, channel_name, plate_number, leave_time, enter_time, reserve_time, deleted, create_time, update_time
    </sql>
    <select id="selectByCarNumber" resultType="java.lang.String">
        select enter_pre_vip_type from  pre_vip_type
        where  plate_number = #{enterCarLicenseNumber} and enter_time =#{enterTime}
    </select>
    <select id="checkByLicenseNumber" resultType="com.parkingmanage.entity.PreVipType">
        select * from pre_vip_type
         where plate_number = #{enterCarLicenseNumber} and enter_time =#{enterTime}
    </select>

</mapper>
