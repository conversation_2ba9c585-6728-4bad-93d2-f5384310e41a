package com.parkingmanage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.parkingmanage.entity.Ownerinfo;
import com.parkingmanage.entity.Violations;
import com.parkingmanage.mapper.OwnerinfoMapper;
import com.parkingmanage.mapper.ViolationsMapper;
import com.parkingmanage.service.ViolationsService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>
 * 违规记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Service
public class ViolationsServiceImpl extends ServiceImpl<ViolationsMapper, Violations> implements ViolationsService {

    @Resource
    private ViolationsMapper violationsMapper;

    @Resource
    private OwnerinfoMapper ownerinfoMapper;

    @Override
    public boolean createViolation(Violations violation) {
        // 查询车主信息
        Integer ownerId = violationsMapper.selectOwnerIdByPlateNumber(violation.getPlateNumber());
        violation.setOwnerId(ownerId);
        
        // 设置创建时间
        violation.setCreatedAt(LocalDateTime.now());
        violation.setUpdatedAt(LocalDateTime.now());
        
        // 保存违规记录
        boolean result = this.save(violation);
        
        // 如果保存成功，更新信用分
        if (result && ownerId != null) {
            updateCreditScoreBySeverity(ownerId, violation.getSeverity());
        }
        
        return result;
    }

    @Override
    public IPage<Map<String, Object>> getViolationsWithOwnerInfo(Page<Map<String, Object>> page, String plateNumber, 
                                                                 String status, String violationType, 
                                                                 LocalDateTime startDate, LocalDateTime endDate, 
                                                                 Integer ownerId) {
        return violationsMapper.selectViolationsWithOwnerInfo(page, plateNumber, status, violationType, 
                                                              startDate, endDate, ownerId);
    }

    @Override
    public boolean updateViolationStatus(Long id, String status, String remark, Integer handlerId) {
        Violations violation = new Violations();
        violation.setId(id);
        violation.setStatus(status);
        violation.setRemark(remark);
        violation.setHandlerId(handlerId);
        violation.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(violation);
    }

    @Override
    public List<Map<String, Object>> getHighRiskVehicles(LocalDateTime startDate, LocalDateTime endDate, Integer limit) {
        return violationsMapper.selectHighRiskVehicles(startDate, endDate, limit);
    }

    @Override
    public Map<String, Object> getViolationStatistics(LocalDateTime startDate, LocalDateTime endDate, String plateNumber) {
        Map<String, Object> result = new HashMap<>();
        
        // 按违规类型统计
        List<Map<String, Object>> typeStats = violationsMapper.selectViolationTypeStats(startDate, endDate);
        result.put("typeStats", typeStats);
        
        // 按日期统计
        List<Map<String, Object>> dailyStats = violationsMapper.selectDailyViolationStats(startDate, endDate, plateNumber);
        result.put("dailyStats", dailyStats);
        
        // 按状态统计
        List<Map<String, Object>> statusStats = violationsMapper.selectViolationStatistics(startDate, endDate, plateNumber);
        result.put("statusStats", statusStats);
        
        return result;
    }

    @Override
    public Map<String, Object> getOwnerByPlateNumber(String plateNumber) {
        LambdaQueryWrapper<Ownerinfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Ownerinfo::getIsaudit, "是");
        wrapper.and(w -> w.like(Ownerinfo::getPlates, plateNumber));
        
        Ownerinfo owner = ownerinfoMapper.selectOne(wrapper);
        if (owner == null) {
            return null;
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("id", owner.getId());
        result.put("name", owner.getOwnername());
        result.put("phone", maskPhone(owner.getOwnerphone()));
        result.put("address", buildAddress(owner));
        result.put("creditScore", owner.getCreditScore());
        result.put("isNewEnergy", plateNumber.length() == 8);
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getPlateSuggestions(String keyword) {
        if (!StringUtils.hasText(keyword) || keyword.length() < 2) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<Ownerinfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Ownerinfo::getIsaudit, "是");
        wrapper.and(w -> w.like(Ownerinfo::getPlates, keyword)
                         .or()
                         .like(Ownerinfo::getOwnername, keyword));
        wrapper.last("LIMIT 10");
        
        List<Ownerinfo> owners = ownerinfoMapper.selectList(wrapper);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Ownerinfo owner : owners) {
            if (StringUtils.hasText(owner.getPlates())) {
                String[] plates = owner.getPlates().split(",");
                for (String plate : plates) {
                    plate = plate.trim();
                    if (plate.toLowerCase().contains(keyword.toLowerCase())) {
                        Map<String, Object> suggestion = new HashMap<>();
                        suggestion.put("plateNumber", plate);
                        suggestion.put("ownerName", owner.getOwnername());
                        suggestion.put("creditScore", owner.getCreditScore());
                        suggestion.put("isNewEnergy", plate.length() == 8);
                        result.add(suggestion);
                    }
                }
            }
        }
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getOwnerVehicles(Integer ownerId) {
        Ownerinfo owner = ownerinfoMapper.selectById(ownerId);
        if (owner == null || !StringUtils.hasText(owner.getPlates())) {
            return new ArrayList<>();
        }
        
        String[] plates = owner.getPlates().split(",");
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (String plate : plates) {
            plate = plate.trim();
            if (StringUtils.hasText(plate)) {
                // 查询该车牌的违规统计
                LambdaQueryWrapper<Violations> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Violations::getPlateNumber, plate);
                
                long totalViolations = this.count(wrapper);
                
                wrapper.ge(Violations::getCreatedAt, LocalDateTime.now().minusMonths(1));
                long monthlyViolations = this.count(wrapper);
                
                Map<String, Object> vehicle = new HashMap<>();
                vehicle.put("plateNumber", plate);
                vehicle.put("isNewEnergy", plate.length() == 8);
                vehicle.put("vehicleType", "car");
                vehicle.put("totalViolations", totalViolations);
                vehicle.put("monthlyViolations", monthlyViolations);
                
                result.add(vehicle);
            }
        }
        
        return result;
    }

    @Override
    public boolean updateOwnerCreditScore(Integer ownerId, Integer creditScore) {
        if (creditScore < 0 || creditScore > 100) {
            return false;
        }
        
        Ownerinfo owner = new Ownerinfo();
        owner.setId(ownerId);
        owner.setCreditScore(creditScore);
        
        return ownerinfoMapper.updateById(owner) > 0;
    }

    /**
     * 根据违规严重程度更新信用分
     */
    private void updateCreditScoreBySeverity(Integer ownerId, String severity) {
        int deduction = 0;
        switch (severity) {
            case "severe":
                deduction = 10;
                break;
            case "moderate":
                deduction = 5;
                break;
            case "mild":
                deduction = 2;
                break;
            default:
                deduction = 5;
        }
        
        violationsMapper.updateOwnerCreditScore(ownerId, deduction);
    }

    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (!StringUtils.hasText(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    /**
     * 构建地址字符串
     */
    private String buildAddress(Ownerinfo owner) {
        StringBuilder address = new StringBuilder();
        if (StringUtils.hasText(owner.getBuilding())) {
            address.append(owner.getBuilding()).append("栋");
        }
        if (owner.getUnits() != null) {
            address.append(owner.getUnits()).append("单元");
        }
        if (owner.getRoomnumber() != null) {
            address.append(owner.getRoomnumber()).append("室");
        }
        return address.toString();
    }
}
