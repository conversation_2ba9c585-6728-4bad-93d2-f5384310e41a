package com.parkingmanage.entity;

import cn.hutool.core.annotation.Alias;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="BlackList对象", description="")
public class BlackList implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @Alias("车场名称")
    private String parkName;
    @Alias("有效期")
    private String blackListForeverFlag;
    @Alias("车牌号码")
    private String carCode;
    @Alias("车主姓名")
    private String owner;
    @Alias("黑名单原因")
    private String reason;
    @Alias("备注1")
    private String remark1;
    @Alias("备注2")
    private String remark2;
    @TableField(exist = false)
    private int specialCarTypeConfigId;
    @Alias("黑名单类型")
    private String specialCarTypeConfigName;
}
