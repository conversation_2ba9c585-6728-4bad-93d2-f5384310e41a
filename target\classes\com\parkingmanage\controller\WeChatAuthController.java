package com.parkingmanage.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.parkingmanage.common.Result;
import com.parkingmanage.entity.Butler;
import com.parkingmanage.entity.Member;
import com.parkingmanage.entity.Ownerinfo;
import com.parkingmanage.entity.VisitorApplication;
import com.parkingmanage.service.ButlerService;
import com.parkingmanage.service.MemberService;
import com.parkingmanage.service.OwnerinfoService;
import com.parkingmanage.service.OwnerRoleVerificationService;
import com.parkingmanage.service.VisitorApplicationService;
import com.parkingmanage.service.WechatMessageService;
import com.parkingmanage.utils.WeChatInfo;
import com.parkingmanage.utils.WeChatUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信小程序授权控制器
 * 支持五层角色判断：管家 → 业主(本地) → 业主(外部API) → 访客申请状态 → 访客
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/parking/wechat")
@Api(tags = "微信小程序授权")
public class WeChatAuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(WeChatAuthController.class);
    
    @Resource
    private ButlerService butlerService;
    
    @Resource
    private OwnerinfoService ownerinfoService;
    
    @Resource
    private MemberService memberService;
    
    @Resource
    private OwnerRoleVerificationService ownerRoleVerificationService;
    
    @Resource
    private VisitorApplicationService visitorApplicationService;
    
    @Resource
    private WechatMessageService wechatMessageService;
    
    /**
     * 微信手机号授权登录
     */
    @ApiOperation("微信手机号授权登录")
    @PostMapping("/phoneAuth")
    public ResponseEntity<Result> phoneAuth(@RequestBody Map<String, String> request) {
        Result result = new Result();
        
        try {
            String code = request.get("code");
            String encryptedData = request.get("encryptedData");
            String iv = request.get("iv");
            String parkName = request.get("parkName"); // 获取停车场名称
            
            // 验证必需参数
            if (code == null || code.trim().isEmpty()) {
                throw new IllegalArgumentException("缺少微信登录凭证code");
            }
            
            // 如果没有传递停车场名称，默认使用"四季上东"
            if (parkName == null || parkName.trim().isEmpty()) {
                parkName = "四季上东";
                logger.info("📍 未传递停车场信息，默认使用: [{}]", parkName);
            }
            
            // 注意：在开发模式下，encryptedData和iv可能为空，因为前端可能没有真实的加密数据
            logger.info("📥 接收到授权请求 - code: [{}], encryptedData: [{}], iv: [{}], parkName: [{}]", 
                code != null ? code.substring(0, Math.min(8, code.length())) + "..." : "null",
                encryptedData != null ? "已提供" : "未提供",
                iv != null ? "已提供" : "未提供",
                parkName);
            
            // 1. 调用微信API获取完整的登录信息
            WeChatInfo weChatInfo = WeChatUtils.getWeChatInfo(code);
            
            // 验证微信API调用结果
            if (!weChatInfo.hasValidInfo()) {
                throw new RuntimeException("微信授权失败: " + weChatInfo.getErrorDescription());
            }
            
            String sessionKey = weChatInfo.getSessionKey();
            String openid = weChatInfo.getOpenid();
            
            // 2. 解密手机号
            String phoneNumber;
            try {
                phoneNumber = WeChatUtils.decryptPhoneNumber(encryptedData, sessionKey, iv);
                
                // 验证手机号格式
                if (!WeChatUtils.isValidPhoneNumber(phoneNumber)) {
                    throw new IllegalArgumentException("获取到的手机号格式不正确: " + phoneNumber);
                }
                logger.info("✅ 成功解密用户手机号: [{}]", phoneNumber);
            } catch (Exception e) {
                logger.error("❌ 手机号解密失败，原因: {}", e.getMessage());
                throw new RuntimeException("手机号解密失败: " + e.getMessage(), e);
            }
            
            logger.info("🔐 微信授权成功 - 手机号: [{}], openid: [{}], unionid: [{}]", 
                phoneNumber, 
                openid.substring(0, Math.min(8, openid.length())) + "...",
                weChatInfo.getUnionid() != null ? weChatInfo.getUnionid().substring(0, Math.min(8, weChatInfo.getUnionid().length())) + "..." : "null");
            // 3. 五层角色判断（传递停车场信息）
            Map<String, Object> userInfo = determineUserRole(phoneNumber, openid, weChatInfo.getUnionid(), parkName);
            result.setData(userInfo);
            result.setCode("0");
            result.setMsg("授权成功");
            
            logger.info("✅ 最终返回给前端的响应: code={}, msg={}, data={}",
                result.getCode(), result.getMsg(), result.getData());
            
        } catch (Exception e) {
            logger.error("❌ 微信授权失败", e);
            result.setCode("1");
            result.setMsg("授权失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 五层角色判断逻辑
     * 1. Butler表（管家） → 2. Ownerinfo表（业主-本地） → 3. 外部API（业主-外部） → 4. VisitorApplication表（访客申请状态） → 5. Member表（访客）
     */
    private Map<String, Object> determineUserRole(String phoneNumber, String openid, String unionid, String parkName) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("phone", phoneNumber);
        userInfo.put("openid", openid);
        userInfo.put("parkName", parkName);
        if (unionid != null) {
            userInfo.put("unionid", unionid);
        }
        
        logger.info("🔍 开始五层角色查询，手机号: [{}], openid: [{}], 停车场: [{}]", phoneNumber, openid, parkName);
        
        // 第一层：查询管家表 (最高优先级) - 只验证手机号
        try {
            logger.info("🔍 第一层查询：Butler表（管家）- 只验证手机号");
            List<Butler> butlerList = butlerService.list();
            logger.info("📊 查询到管家总数: {}", butlerList.size());

            // 只匹配手机号
            Butler butler = butlerList.stream()
                .filter(b -> phoneNumber.equals(b.getPhone()))
                .findFirst()
                .orElse(null);

            if (butler != null) {
                logger.info("✅ 第一层查询成功：找到管家角色（手机号匹配）");
                userInfo.put("role", "manager");
                userInfo.put("roleText", "管家");
                userInfo.put("userInfo", butler);
                userInfo.put("permissions", Arrays.asList(getManagerPermissions()));
                userInfo.put("source", "butler_table");
                userInfo.put("verification", "phone_only");
                return userInfo;
            }

            logger.info("❌ 第一层查询：未找到管家（手机号不匹配），继续下一层查询");
        } catch (Exception e) {
            logger.error("❌ 第一层查询异常，继续下一层", e);
        }
        
        // 第二层：查询业主表（本地数据）- 只验证手机号
        try {
            logger.info("🔍 第二层查询：Ownerinfo表（业主-本地）- 验证手机号");
            List<Ownerinfo> ownerList = ownerinfoService.phoneNumberOwnerInfo(phoneNumber);
            logger.info("📊 业主表查询结果数量: {}", ownerList.size());
            
            if (!ownerList.isEmpty()) {
                Ownerinfo owner = ownerList.get(0);
                logger.info("✅ 第二层查询成功：找到业主角色（本地数据，手机号匹配）");
                userInfo.put("role", "owner");
                userInfo.put("roleText", "业主");
                userInfo.put("userInfo", owner);
                userInfo.put("permissions", Arrays.asList(getOwnerPermissions()));
                userInfo.put("source", "ownerinfo_table");
                userInfo.put("verification", "phone_only");
                return userInfo;
            }
            
            logger.info("❌ 第二层查询：本地业主表无记录，继续外部API查询");
        } catch (Exception e) {
            logger.error("❌ 第二层查询异常，继续下一层", e);
        }
        
        // 第三层：查询外部API（业主补充验证）- 只验证手机号（指定停车场）
        try {
            logger.info("🔍 第三层查询：外部API（业主-外部）- 验证手机号，停车场: [{}]", parkName);
            boolean isOwnerFromAPI = ownerRoleVerificationService.isOwnerByPhoneNumberInPark(phoneNumber, parkName);
            
            if (isOwnerFromAPI) {
                logger.info("✅ 第三层查询成功：找到业主角色（外部API，停车场: [{}]，手机号匹配）", parkName);
                
                // 获取外部API的业主详细信息
                Map<String, Object> apiOwnerInfo = ownerRoleVerificationService.getOwnerDetailsByPark(phoneNumber, parkName);
                
                userInfo.put("role", "owner");
                userInfo.put("roleText", "业主");
                userInfo.put("userInfo", apiOwnerInfo);
                userInfo.put("permissions", Arrays.asList(getOwnerPermissions()));
                userInfo.put("source", "external_api");
                userInfo.put("verification", "phone_only");
                userInfo.put("parkName", parkName);
                userInfo.put("needSync", true); // 标记需要同步到本地数据库
                return userInfo;
            }
            
            logger.info("❌ 第三层查询：外部API无记录（停车场: [{}]），继续Member表查询", parkName);
        } catch (Exception e) {
            logger.warn("⚠️ 第三层查询：外部API调用异常（停车场: [{}]），继续下一层查询", parkName, e);
        }
        
        // 第四层：查询访客申请表（visitor_application）- 验证手机号和审核状态
        try {
            logger.info("🔍 第四层查询：visitor_application表（访客申请状态）- 验证手机号");
            VisitorApplication visitorApplication = visitorApplicationService.getByPhone(phoneNumber);
            
            if (visitorApplication != null) {
                String auditStatus = visitorApplication.getAuditstatus();
                logger.info("✅ 第四层查询成功：找到访客申请记录，状态: [{}]", auditStatus);
                
                if ("待审批".equals(auditStatus)) {
                    // 有待审核的申请
                    logger.info("⏳ 访客申请待审核状态，提示用户等待");
                    userInfo.put("role", "pending");
                    userInfo.put("roleText", "待审核");
                    userInfo.put("userInfo", visitorApplication);
                    userInfo.put("permissions", Arrays.asList(new String[]{}));
                    userInfo.put("source", "visitor_application_table");
                    userInfo.put("verification", "phone_only");
                    userInfo.put("message", "您的访客申请正在审核中，请耐心等待管理员审核");
                    userInfo.put("applicationNo", visitorApplication.getApplicationNo());
                    return userInfo;
                    
                } else if ("已通过".equals(auditStatus)) {
                    // 申请已通过，直接进入系统
                    logger.info("✅ 访客申请已通过，直接进入系统");
                    userInfo.put("role", "visitor");
                    userInfo.put("roleText", "访客");
                    userInfo.put("userInfo", visitorApplication);
                    userInfo.put("permissions", Arrays.asList(getVisitorPermissions()));
                    userInfo.put("source", "visitor_application_table");
                    userInfo.put("verification", "phone_only");
                    userInfo.put("message", "欢迎访客用户");
                    userInfo.put("applicationNo", visitorApplication.getApplicationNo());
                    return userInfo;
                    
                } else if ("未通过".equals(auditStatus)) {
                    // 申请未通过，可以重新申请
                    logger.info("❌ 访客申请未通过，用户可重新申请");
                    userInfo.put("role", "rejected");
                    userInfo.put("roleText", "申请被拒");
                    userInfo.put("userInfo", visitorApplication);
                    userInfo.put("permissions", Arrays.asList(new String[]{}));
                    userInfo.put("source", "visitor_application_table");
                    userInfo.put("verification", "phone_only");
                    userInfo.put("message", "您的访客申请未通过审核，可以重新申请。拒绝原因：" + 
                        (visitorApplication.getRefusereason() != null ? visitorApplication.getRefusereason() : "无"));
                    userInfo.put("applicationNo", visitorApplication.getApplicationNo());
                    userInfo.put("canReapply", true);
                    return userInfo;
                } else {
                    logger.warn("⚠️ 访客申请状态异常: [{}]", auditStatus);
                }
            } else {
                logger.info("❌ 第四层查询：visitor_application表中未找到记录，继续Member表查询");
            }
            
        } catch (Exception e) {
            logger.error("❌ 第四层查询异常，继续下一层", e);
        }
        
        // 第五层：查询Member表（访客）- 同时验证openid和手机号
        try {
            logger.info("🔍 第五层查询：Member表（访客）- 验证openid和手机号");
            
            // 通过openid查询Member
            Member member = memberService.getMemberByOpenId(openid);
            
            if (member != null) {
                // 同时验证手机号是否匹配
                if (phoneNumber.equals(member.getUserphone())) {
                    logger.info("✅ 第五层查询成功：找到访客记录（openid和手机号都匹配）");
                    
                    // Member表中没有auditstatus字段，但可以根据userkind判断状态
                    // 这里假设存在的记录就是已通过的访客
                    userInfo.put("role", "visitor");
                    userInfo.put("roleText", "访客");
                    userInfo.put("userInfo", member);
                    userInfo.put("permissions", Arrays.asList(getVisitorPermissions()));
                    userInfo.put("source", "member_table");
                    userInfo.put("verification", "openid_and_phone");
                    return userInfo;
                } else {
                    logger.warn("⚠️ 第五层查询：找到openid记录但手机号不匹配，openid: [{}], 记录中手机号: [{}], 当前手机号: [{}]", 
                        openid.substring(0, Math.min(8, openid.length())) + "...",
                        member.getUserphone(), phoneNumber);
                }
            } else {
                logger.info("❌ 第五层查询：Member表中未找到openid记录");
            }
            
        } catch (Exception e) {
            logger.error("❌ 第五层查询异常", e);
        }
        
        // 完全未注册的用户
        logger.info("📝 所有层级查询完毕：用户未注册");
        userInfo.put("role", "unregistered");
        userInfo.put("roleText", "未注册");
        userInfo.put("userInfo", null);
        userInfo.put("permissions", Arrays.asList(new String[]{}));
        userInfo.put("source", "none");
        userInfo.put("message", "请先申请注册");
        
        return userInfo;
    }
    
    /**
     * 通过code获取用户openid
     */
    @ApiOperation("通过code获取用户openid")
    @GetMapping("/getOpenid")
    public ResponseEntity<Result> getOpenidByCode(@RequestParam String code) {
        Result result = new Result();
        
        try {
            logger.info("📥 接收到获取openid请求 - code: [{}]", 
                code != null ? code.substring(0, Math.min(8, code.length())) + "..." : "null");
            
            // 调用微信API获取完整的登录信息
            WeChatInfo weChatInfo = WeChatUtils.getWeChatInfo(code);
            
            // 验证微信API调用结果
            if (!weChatInfo.hasValidInfo()) {
                throw new RuntimeException("微信授权失败: " + weChatInfo.getErrorDescription());
            }
            
            String openid = weChatInfo.getOpenid();
            String unionid = weChatInfo.getUnionid();
            
            logger.info("✅ 成功获取用户openid: [{}], unionid: [{}]", 
                openid.substring(0, Math.min(8, openid.length())) + "...",
                unionid != null ? unionid.substring(0, Math.min(8, unionid.length())) + "..." : "null");
            
            Map<String, String> data = new HashMap<>();
            data.put("openid", openid);
            if (unionid != null) {
                data.put("unionid", unionid);
            }
            
            result.setData(data);
            result.setCode("0");
            result.setMsg("获取openid成功");
            
        } catch (Exception e) {
            logger.error("❌ 获取openid失败", e);
            result.setCode("1");
            result.setMsg("获取openid失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 检查用户是否关注了公众号
     */
    @ApiOperation("检查用户是否关注了公众号")
    @GetMapping("/checkSubscription")
    public ResponseEntity<Result> checkUserSubscription(@RequestParam String openid) {
        Result result = new Result();
        
        try {
            logger.info("📥 接收到检查公众号关注状态请求 - openid: [{}]", 
                openid != null ? openid.substring(0, Math.min(8, openid.length())) + "..." : "null");
            
            // 调用服务检查用户是否关注了公众号
            boolean isFollowed = wechatMessageService.checkUserSubscription(openid);
            
            logger.info("✅ 用户 [{}] 公众号关注状态: {}", 
                openid.substring(0, Math.min(8, openid.length())) + "...",
                isFollowed ? "已关注" : "未关注");
            
            Map<String, Object> data = new HashMap<>();
            data.put("isFollowed", isFollowed);
            data.put("openid", openid);
            // 如果已关注，可以添加关注时间等信息（需要微信API支持）
            
            result.setData(data);
            result.setCode("0");
            result.setMsg("检查公众号关注状态成功");
            
        } catch (Exception e) {
            logger.error("❌ 检查公众号关注状态失败", e);
            result.setCode("1");
            result.setMsg("检查公众号关注状态失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取管家权限列表
     */
    private String[] getManagerPermissions() {
        return new String[]{
            "appointment.query",        // 预约查询
            "appointment.audit",        // 预约审核
            "appointment.query.all",    // 查询所有预约
            "violation.manage",         // 违规管理
            "violation.view.all",       // 查看所有违规
            "audit.member",            // 审核会员申请
            "audit.appointment",       // 审核预约申请
            "owner.manage",            // 业主管理
            "manage.facility"          // 设施管理
        };
    }
    
    /**
     * 获取业主权限列表
     */
    private String[] getOwnerPermissions() {
        return new String[]{
            "appointment.create",       // 创建预约
            "appointment.query.own",    // 查询个人预约
            "appointment.cancel",       // 取消预约
            "violation.view.own",       // 查看个人违规
            "violation.report"          // 举报违规
        };
    }
    
    /**
     * 获取访客权限列表（最小权限，仅预约相关）
     */
    private String[] getVisitorPermissions() {
        return new String[]{
            "visitor.appointment",      // 访客预约（专用）
            "visitor.query",           // 访客查询（专用）
            "appointment.query.own"    // 查询个人预约（基础）
        };
    }
    
    /**
     * 检查用户权限
     */
    @ApiOperation("检查用户权限")
    @GetMapping("/checkPermission")
    public ResponseEntity<Result> checkPermission(
            @RequestParam String phoneNumber,
            @RequestParam String permission,
            @RequestParam(required = false) String openid,
            @RequestParam(required = false, defaultValue = "四季上东") String parkName) {
        Result result = new Result();
        
        try {
            Map<String, Object> userInfo = determineUserRole(phoneNumber, openid, null, parkName);
            @SuppressWarnings("unchecked")
            List<String> permissions = (List<String>) userInfo.get("permissions");
            
            boolean hasPermission = false;
            for (String p : permissions) {
                if (p.equals(permission)) {
                    hasPermission = true;
                    break;
                }
            }

            Map<String, Object> data = new HashMap<>();
            data.put("hasPermission", hasPermission);
            data.put("userRole", userInfo.get("role"));
            data.put("roleText", userInfo.get("roleText"));
            data.put("source", userInfo.get("source"));
            
            result.setData(data);
            result.setCode("0");
            result.setMsg("检查完成");
            
        } catch (Exception e) {
            logger.error("❌ 权限检查失败", e);
            result.setCode("1");
            result.setMsg("权限检查失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取角色统计信息（调试用）
     */
    @ApiOperation("获取角色统计信息")
    @GetMapping("/roleStats")
    public ResponseEntity<Result> getRoleStats() {
        Result result = new Result();
        
        try {
            Map<String, Object> stats = ownerRoleVerificationService.getStatistics();
            
            // 添加本地数据统计
            long butlerCount = butlerService.count();
            long ownerCount = ownerinfoService.count();
            long memberCount = memberService.count();
            
            stats.put("butlerCount", butlerCount);
            stats.put("ownerCount", ownerCount);
            stats.put("memberCount", memberCount);
            
            result.setData(stats);
            result.setCode("0");
            result.setMsg("统计信息获取成功");
            
        } catch (Exception e) {
            logger.error("❌ 获取统计信息失败", e);
            result.setCode("1");
            result.setMsg("获取统计信息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
} 