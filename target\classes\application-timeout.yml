# 停车超时提醒功能配置文件
# 将此配置合并到主配置文件 application.yml 中

# 微信公众号配置
wechat:
  # 微信公众号AppID
  appid: wx_your_appid_here
  # 微信公众号AppSecret  
  secret: your_app_secret_here
  # 超时提醒模板消息配置
  timeout:
    template:
      # 模板消息ID（您提供的实际模板ID）
      id: 45414
      # 模板编号
      code: NvxG4JJ8SSfhVh1cdK0Jadz09dIN9icHlYooawCE_5k

# Spring任务调度配置
spring:
  task:
    scheduling:
      # 线程池大小
      pool:
        size: 2
  # 启用定时任务
  scheduling:
    enabled: true
  # 异步任务配置
  async:
    core-pool-size: 5
    max-pool-size: 20
    queue-capacity: 200

# 停车超时提醒功能配置
parking:
  timeout:
    # 定时任务执行间隔（毫秒）默认5分钟
    check-interval: 300000
    # 提醒时间（分钟）- 在超时前多少分钟提醒
    warning-minutes: 15
    # 是否启用超时提醒功能
    enabled: true
    # 默认超时时间配置（分钟）
    default-timeout:
      temp: 120      # 临时车辆2小时
      visitor: 180   # 访客车辆3小时  
      owner: 720     # 业主车辆12小时
    # 重试配置
    retry:
      # 最大重试次数
      max-attempts: 3
      # 重试间隔（毫秒）
      delay: 30000

# 日志配置
logging:
  level:
    com.parkingmanage.service.ParkingTimeoutNotificationService: INFO
    com.parkingmanage.service.WechatMessageService: INFO
    com.parkingmanage.controller.WechatTestController: INFO
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/parking-timeout.log
    max-size: 10MB
    max-history: 30

# 数据库连接池配置（如果需要调整）
spring:
  datasource:
    hikari:
      # 连接池最大连接数
      maximum-pool-size: 20
      # 最小空闲连接数
      minimum-idle: 5
      # 连接超时时间
      connection-timeout: 30000
      # 空闲连接最大存活时间
      idle-timeout: 600000

# MyBatis配置
mybatis-plus:
  configuration:
    # 打印SQL语句（生产环境建议关闭）
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 逻辑删除配置
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
# 监控配置（可选）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    tags:
      application: parking-timeout-system 