/*
 Navicat Premium Dump SQL

 Source Server         : localhost3306
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : project_lzx

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 31/07/2025 17:04:24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for violations
-- ----------------------------
DROP TABLE IF EXISTS `violations`;
CREATE TABLE `violations`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `plate_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车牌号',
  `owner_id` int NULL DEFAULT NULL COMMENT '车主ID（关联ownerinfo表）',
  `violation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '违规类型',
  `custom_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '自定义违规类型',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '违规位置',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '违规描述',
  `appointment_time` datetime NULL DEFAULT NULL COMMENT '预约时间',
  `enter_time` datetime NULL DEFAULT NULL COMMENT '进场时间',
  `leave_time` datetime NULL DEFAULT NULL COMMENT '离场时间',
  `status` enum('pending','processing','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '处理状态',
  `severity` enum('mild','moderate','severe') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'moderate' COMMENT '严重程度',
  `reporter_id` int NULL DEFAULT NULL COMMENT '举报人ID',
  `handler_id` int NULL DEFAULT NULL COMMENT '处理人ID',
  `photos` json NULL COMMENT '现场照片',
  `voice_memo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '语音备注文件路径',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_plate_number`(`plate_number` ASC) USING BTREE,
  INDEX `idx_owner_id`(`owner_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_violation_type`(`violation_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_appointment_time`(`appointment_time` ASC) USING BTREE,
  CONSTRAINT `fk_violations_owner` FOREIGN KEY (`owner_id`) REFERENCES `ownerinfo` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '违规记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of violations (最近一周数据: 2025-07-25 到 2025-07-31)
-- ----------------------------
UPDATE `violations` SET `plate_number` = '黑A12345', `owner_id` = 71, `violation_type` = '超时停车', `custom_type` = NULL, `location` = 'A区-15号车位', `description` = '超时停车2小时', `appointment_time` = '2025-07-25 14:00:00', `enter_time` = '2025-07-25 14:00:00', `leave_time` = '2025-07-25 16:30:00', `status` = 'completed', `severity` = 'moderate', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-25 14:00:00', `updated_at` = '2025-07-31 15:42:05' WHERE `id` = 1;
UPDATE `violations` SET `plate_number` = '黑A54321', `owner_id` = 72, `violation_type` = '未按位停车', `custom_type` = NULL, `location` = 'B区-08号车位', `description` = '未按指定位置停车', `appointment_time` = '2025-07-28 09:00:00', `enter_time` = '2025-07-28 09:00:00', `leave_time` = '2025-07-28 11:15:00', `status` = 'completed', `severity` = 'mild', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-28 09:00:00', `updated_at` = '2025-07-31 15:42:14' WHERE `id` = 2;
UPDATE `violations` SET `plate_number` = '黑A5C5KK', `owner_id` = 73, `violation_type` = '占用他人车位', `custom_type` = NULL, `location` = 'C区-22号车位', `description` = '占用他人预约车位', `appointment_time` = '2025-07-30 16:00:00', `enter_time` = '2025-07-30 16:00:00', `leave_time` = NULL, `status` = 'processing', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-30 16:00:00', `updated_at` = '2025-07-31 15:42:38' WHERE `id` = 3;
UPDATE `violations` SET `plate_number` = '黑A145K2', `owner_id` = 75, `violation_type` = '超时停车', `custom_type` = NULL, `location` = 'A区-10号车位', `description` = '超时停车1.5小时', `appointment_time` = '2025-07-26 10:00:00', `enter_time` = '2025-07-26 10:00:00', `leave_time` = '2025-07-26 12:00:00', `status` = 'completed', `severity` = 'moderate', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-26 10:00:00', `updated_at` = '2025-07-31 15:43:03' WHERE `id` = 4;
UPDATE `violations` SET `plate_number` = '黑A784KK', `owner_id` = 76, `violation_type` = '占用残疾人车位', `custom_type` = NULL, `location` = 'D区-残疾人专用位', `description` = '占用残疾人车位', `appointment_time` = '2025-07-27 15:00:00', `enter_time` = '2025-07-27 15:00:00', `leave_time` = '2025-07-27 17:30:00', `status` = 'completed', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-27 15:00:00', `updated_at` = '2025-07-31 15:43:11' WHERE `id` = 5;
UPDATE `violations` SET `plate_number` = '黑AD55CJK', `owner_id` = 73, `violation_type` = '未按位停车', `custom_type` = NULL, `location` = 'E区-新能源专用位', `description` = '普通车占用新能源车位', `appointment_time` = '2025-07-29 11:00:00', `enter_time` = '2025-07-29 11:00:00', `leave_time` = '2025-07-29 13:20:00', `status` = 'completed', `severity` = 'mild', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-29 11:00:00', `updated_at` = '2025-07-31 15:44:50' WHERE `id` = 6;
UPDATE `violations` SET `plate_number` = '黑A45KD4', `owner_id` = 77, `violation_type` = '超时停车', `custom_type` = NULL, `location` = 'E区-新能源专用位', `description` = '新能源车超时停车', `appointment_time` = '2025-07-25 13:00:00', `enter_time` = '2025-07-25 13:00:00', `leave_time` = '2025-07-25 15:45:00', `status` = 'completed', `severity` = 'moderate', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-25 13:00:00', `updated_at` = '2025-07-31 15:45:09' WHERE `id` = 7;
UPDATE `violations` SET `plate_number` = '黑A12345', `owner_id` = 71, `violation_type` = '堵塞通道', `custom_type` = NULL, `location` = 'F区-通道口', `description` = '车辆堵塞消防通道', `appointment_time` = '2025-07-31 08:00:00', `enter_time` = '2025-07-31 08:00:00', `leave_time` = NULL, `status` = 'pending', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-31 08:00:00', `updated_at` = '2025-07-31 15:46:15' WHERE `id` = 8;
UPDATE `violations` SET `plate_number` = '黑A54321', `owner_id` = 72, `violation_type` = '未经授权停车', `custom_type` = NULL, `location` = 'G区-管理区域', `description` = '未经授权在管理区域停车', `appointment_time` = '2025-07-31 14:00:00', `enter_time` = '2025-07-31 14:00:00', `leave_time` = NULL, `status` = 'pending', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-31 14:00:00', `updated_at` = '2025-07-31 14:00:00' WHERE `id` = 9;

-- 添加更多最近一周的违规记录，让数据更丰富
UPDATE `violations` SET `plate_number` = '黑A12345', `owner_id` = 71, `violation_type` = '超时停车', `custom_type` = NULL, `location` = 'A区-20号车位', `description` = '超时停车3小时', `appointment_time` = '2025-07-26 16:00:00', `enter_time` = '2025-07-26 16:00:00', `leave_time` = '2025-07-26 19:30:00', `status` = 'completed', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-26 16:00:00', `updated_at` = '2025-07-31 16:00:00' WHERE `id` = 10;
UPDATE `violations` SET `plate_number` = '黑A5C5KK', `owner_id` = 73, `violation_type` = '压线停车', `custom_type` = NULL, `location` = 'B区-12号车位', `description` = '车辆压线停车', `appointment_time` = '2025-07-27 10:30:00', `enter_time` = '2025-07-27 10:30:00', `leave_time` = '2025-07-27 12:00:00', `status` = 'completed', `severity` = 'mild', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-27 10:30:00', `updated_at` = '2025-07-31 16:05:00' WHERE `id` = 11;
UPDATE `violations` SET `plate_number` = '黑A784KK', `owner_id` = 76, `violation_type` = '超时停车', `custom_type` = NULL, `location` = 'C区-05号车位', `description` = '超时停车4小时', `appointment_time` = '2025-07-28 14:00:00', `enter_time` = '2025-07-28 14:00:00', `leave_time` = '2025-07-28 18:30:00', `status` = 'completed', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-28 14:00:00', `updated_at` = '2025-07-31 16:10:00' WHERE `id` = 12;
UPDATE `violations` SET `plate_number` = '黑AD55CJK', `owner_id` = 73, `violation_type` = '占用VIP车位', `custom_type` = NULL, `location` = 'D区-VIP专用位', `description` = '普通车占用VIP车位', `appointment_time` = '2025-07-29 08:00:00', `enter_time` = '2025-07-29 08:00:00', `leave_time` = '2025-07-29 10:30:00', `status` = 'completed', `severity` = 'moderate', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-29 08:00:00', `updated_at` = '2025-07-31 16:15:00' WHERE `id` = 13;
UPDATE `violations` SET `plate_number` = '黑A45KD4', `owner_id` = 77, `violation_type` = '未按位停车', `custom_type` = NULL, `location` = 'E区-18号车位', `description` = '新能源车未按位停车', `appointment_time` = '2025-07-30 12:00:00', `enter_time` = '2025-07-30 12:00:00', `leave_time` = '2025-07-30 14:15:00', `status` = 'completed', `severity` = 'mild', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-30 12:00:00', `updated_at` = '2025-07-31 16:20:00' WHERE `id` = 14;
UPDATE `violations` SET `plate_number` = '黑A145K2', `owner_id` = 75, `violation_type` = '堵塞通道', `custom_type` = NULL, `location` = 'F区-安全出口', `description` = '车辆堵塞安全通道', `appointment_time` = '2025-07-31 09:30:00', `enter_time` = '2025-07-31 09:30:00', `leave_time` = NULL, `status` = 'processing', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-31 09:30:00', `updated_at` = '2025-07-31 16:25:00' WHERE `id` = 15;
UPDATE `violations` SET `plate_number` = '黑A12345', `owner_id` = 71, `violation_type` = '占用他人车位', `custom_type` = NULL, `location` = 'A区-25号车位', `description` = '占用他人预约车位', `appointment_time` = '2025-07-25 11:00:00', `enter_time` = '2025-07-25 11:00:00', `leave_time` = '2025-07-25 13:30:00', `status` = 'completed', `severity` = 'moderate', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-25 11:00:00', `updated_at` = '2025-07-31 16:30:00' WHERE `id` = 16;
UPDATE `violations` SET `plate_number` = '黑A54321', `owner_id` = 72, `violation_type` = '超时停车', `custom_type` = NULL, `location` = 'B区-30号车位', `description` = '超时停车2.5小时', `appointment_time` = '2025-07-26 13:00:00', `enter_time` = '2025-07-26 13:00:00', `leave_time` = '2025-07-26 15:45:00', `status` = 'completed', `severity` = 'moderate', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-26 13:00:00', `updated_at` = '2025-07-31 16:35:00' WHERE `id` = 17;
UPDATE `violations` SET `plate_number` = '黑A5C5KK', `owner_id` = 73, `violation_type` = '未按位停车', `custom_type` = NULL, `location` = 'C区-35号车位', `description` = '车辆未按指定位置停车', `appointment_time` = '2025-07-27 17:00:00', `enter_time` = '2025-07-27 17:00:00', `leave_time` = '2025-07-27 19:20:00', `status` = 'completed', `severity` = 'mild', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-27 17:00:00', `updated_at` = '2025-07-31 16:40:00' WHERE `id` = 18;
UPDATE `violations` SET `plate_number` = '黑A784KK', `owner_id` = 76, `violation_type` = '占用残疾人车位', `custom_type` = NULL, `location` = 'D区-残疾人专用位2', `description` = '再次占用残疾人车位', `appointment_time` = '2025-07-28 16:30:00', `enter_time` = '2025-07-28 16:30:00', `leave_time` = '2025-07-28 18:00:00', `status` = 'completed', `severity` = 'severe', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-28 16:30:00', `updated_at` = '2025-07-31 16:45:00' WHERE `id` = 19;
UPDATE `violations` SET `plate_number` = '黑AD55CJK', `owner_id` = 73, `violation_type` = '压线停车', `custom_type` = NULL, `location` = 'E区-40号车位', `description` = '车辆严重压线', `appointment_time` = '2025-07-29 15:30:00', `enter_time` = '2025-07-29 15:30:00', `leave_time` = '2025-07-29 17:00:00', `status` = 'completed', `severity` = 'mild', `reporter_id` = NULL, `handler_id` = NULL, `photos` = NULL, `voice_memo` = NULL, `remark` = NULL, `created_at` = '2025-07-29 15:30:00', `updated_at` = '2025-07-31 16:50:00' WHERE `id` = 20;

-- ----------------------------
-- Triggers structure for table violations
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_violations_after_insert`;
delimiter ;;
CREATE TRIGGER `tr_violations_after_insert` AFTER INSERT ON `violations` FOR EACH ROW BEGIN
    IF NEW.owner_id IS NOT NULL THEN
        CALL UpdateOwnerCreditScore(NEW.owner_id, NEW.severity);
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
