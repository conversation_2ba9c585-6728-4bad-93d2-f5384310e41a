<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.SupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Supplier">
        <id column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_address" property="supplierAddress" />
        <result column="contacts_person" property="contactsPerson" />
        <result column="telephone" property="telephone" />
        <result column="registration_time" property="registrationTime" />
        <result column="remarks" property="remarks" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        supplier_id, supplier_name, supplier_code, supplier_address, contacts_person, telephone, registration_time, remarks, deleted
    </sql>

</mapper>
