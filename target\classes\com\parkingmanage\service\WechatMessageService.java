package com.parkingmanage.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.parkingmanage.common.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信消息服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class WechatMessageService {

    @Value("${wechat.appid}")
    private String appId;

    @Value("${wechat.secret}")
    private String appSecret;

    @Value("${wechat.timeout.template.id}")
    private String timeoutTemplateId;

    private String accessToken;
    private Date tokenExpireTime;

    /**
     * 检查用户是否关注公众号
     */
    public boolean checkUserSubscription(String openid) {
        try {
            String token = getAccessToken();
            if (StringUtils.isEmpty(token)) {
                log.error("获取AccessToken失败");
                return false;
            }

            String url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=" + token + "&openid=" + openid;
            String response = HttpClientUtil.doGet(url);
            JSONObject jsonResponse = JSON.parseObject(response);

            // subscribe=1表示已关注，0表示未关注
            return jsonResponse.getIntValue("subscribe") == 1;
        } catch (Exception e) {
            log.error("检查用户关注状态失败", e);
            return false;
        }
    }

    /**
     * 发送超时提醒消息
     */
    public boolean sendTimeoutWarningMessage(String openid, String plateNumber, 
                                           String yardName, Date expectedLeaveTime, Date enterTime) {
        try {
            // 获取AccessToken
            String token = getAccessToken();
            if (StringUtils.isEmpty(token)) {
                log.error("获取AccessToken失败");
                return false;
            }

            // 构造消息数据
            Map<String, Object> messageData = buildMessageData(plateNumber, yardName, expectedLeaveTime, enterTime);

            // 构造请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("touser", openid);
            requestBody.put("template_id", timeoutTemplateId);
            requestBody.put("data", messageData);

            // 发送消息
            String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + token;
            String response = HttpClientUtil.doPostJson(url, JSON.toJSONString(requestBody));

            log.info("微信消息发送响应：{}", response);

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);
            int errcode = jsonResponse.getIntValue("errcode");

            return errcode == 0;

        } catch (Exception e) {
            log.error("发送微信消息失败", e);
            return false;
        }
    }

    /**
     * 获取AccessToken
     */
    private String getAccessToken() {
        // 如果token未过期，直接返回
        if (accessToken != null && tokenExpireTime != null && 
            new Date().before(tokenExpireTime)) {
            return accessToken;
        }

        try {
            Map<String, String> params = new HashMap<>();
            params.put("grant_type", "client_credential");
            params.put("appid", appId);
            params.put("secret", appSecret);

            String response = HttpClientUtil.doPost("https://api.weixin.qq.com/cgi-bin/token", params);
            JSONObject jsonResponse = JSON.parseObject(response);

            if (jsonResponse.containsKey("access_token")) {
                accessToken = jsonResponse.getString("access_token");
                int expiresIn = jsonResponse.getIntValue("expires_in");

                // 设置过期时间（提前5分钟过期）
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.SECOND, expiresIn - 300);
                tokenExpireTime = calendar.getTime();

                return accessToken;
            } else {
                log.error("获取AccessToken失败：{}", response);
                return null;
            }

        } catch (Exception e) {
            log.error("获取AccessToken异常", e);
            return null;
        }
    }

    /**
     * 构造消息数据
     */
    private Map<String, Object> buildMessageData(String plateNumber, String yardName, Date expectedLeaveTime, Date enterTime) {
        Map<String, Object> data = new HashMap<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm");

        // 计算剩余时长
        long remainingMinutes = (expectedLeaveTime.getTime() - new Date().getTime()) / (60 * 1000);
        String remainingTime = remainingMinutes > 0 ? remainingMinutes + "分钟" : "已超时";

        // 根据您的微信模板消息格式构造数据
        data.put("thing2", createDataItem(yardName, "#173177"));           // 停车场名称
        data.put("car_number5", createDataItem(plateNumber, "#173177"));   // 车牌号
        data.put("const13", createDataItem(remainingTime, "#FF0000"));     // 剩余时长
        data.put("time9", createDataItem(sdf.format(enterTime), "#173177")); // 入场时间
        data.put("time11", createDataItem(sdf.format(new Date()), "#173177")); // 通知时间

        return data;
    }

    /**
     * 创建数据项
     */
    private Map<String, String> createDataItem(String value, String color) {
        Map<String, String> item = new HashMap<>();
        item.put("value", value);
        item.put("color", color);
        return item;
    }
} 