package com.parkingmanage.controller;

import com.parkingmanage.common.Result;
import com.parkingmanage.dto.MockAppointmentRequest;
import com.parkingmanage.dto.TestMessageRequest;
import com.parkingmanage.service.WechatMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信功能测试控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
@Api(tags = "微信功能测试")
@Slf4j
public class WechatTestController {

    @Autowired
    private WechatMessageService wechatMessageService;

    /**
     * 测试检查用户关注状态
     */
    @GetMapping("/checkSubscription")
    @ApiOperation(value = "检查用户关注状态")
    public ResponseEntity<Result> checkSubscription(@RequestParam String openid) {
        try {
            boolean isSubscribed = wechatMessageService.checkUserSubscription(openid);

            Result result = new Result();
            result.setCode("0");
            result.setData(isSubscribed);
            result.setMsg(isSubscribed ? "用户已关注公众号" : "用户未关注公众号");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查用户关注状态失败", e);
            Result result = new Result();
            result.setCode("1");
            result.setMsg("检查失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 测试发送模板消息
     */
    @PostMapping("/sendTimeoutMessage")
    @ApiOperation(value = "发送测试超时提醒消息")
    public ResponseEntity<Result> sendTimeoutMessage(@RequestBody TestMessageRequest request) {
        try {
            // 检查用户是否关注
            boolean isSubscribed = wechatMessageService.checkUserSubscription(request.getOpenid());
            if (!isSubscribed) {
                Result result = new Result();
                result.setCode("1");
                result.setMsg("用户未关注公众号，无法发送模板消息");
                return ResponseEntity.ok(result);
            }

            // 构造测试数据
            Date now = new Date();
            Date enterTime = new Date(now.getTime() - 60 * 60 * 1000); // 1小时前进场
            Date expectedLeaveTime = new Date(now.getTime() + 15 * 60 * 1000); // 15分钟后超时

            // 发送测试消息
            boolean success = wechatMessageService.sendTimeoutWarningMessage(
                request.getOpenid(),
                request.getPlateNumber(),
                request.getYardName(),
                expectedLeaveTime,
                enterTime
            );

            Result result = new Result();
            result.setCode(success ? "0" : "1");
            result.setData(success);
            result.setMsg(success ? "模板消息发送成功" : "模板消息发送失败");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("发送测试消息失败", e);
            Result result = new Result();
            result.setCode("1");
            result.setMsg("发送失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 模拟预约接口（用于测试引导关注）
     */
    @PostMapping("/mockAppointment")
    @ApiOperation(value = "模拟预约接口（测试引导关注）")
    public ResponseEntity<Result> mockAppointment(@RequestBody MockAppointmentRequest request) {
        try {
            Result result = new Result();
            result.setCode("0");
            result.setMsg("预约成功！");

            // 检查用户是否关注公众号
            if (!StringUtils.isEmpty(request.getOpenid())) {
                boolean isSubscribed = wechatMessageService.checkUserSubscription(request.getOpenid());
                if (!isSubscribed) {
                    result.setMsg("预约成功！为了及时接收停车提醒，建议您关注我们的公众号。");
                    result.setCode("NEED_SUBSCRIBE");
//                    result.setData("NEED_SUBSCRIBE")
                    // 返回公众号二维码信息
                    Map<String, Object> extraData = new HashMap<>();
                    extraData.put("qrcode_url", "https://your-domain.com/qrcode.jpg");
                    extraData.put("subscribe_tip", "扫描二维码关注公众号，及时接收停车提醒");
                    extraData.put("is_subscribed", false);
                    result.setData(extraData);
                } else {
                    Map<String, Object> extraData = new HashMap<>();
                    extraData.put("is_subscribed", true);
                    result.setData(extraData);
                }
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("模拟预约失败", e);
            Result result = new Result();
            result.setCode("1");
            result.setMsg("预约失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取微信配置信息（用于调试）
     */
    @GetMapping("/wechatConfig")
    @ApiOperation(value = "获取微信配置信息")
    public ResponseEntity<Result> getWechatConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("status", "配置信息已加载");
            config.put("timestamp", new Date());
            
            Result result = new Result();
            result.setCode("0");
            result.setData(config);
            result.setMsg("获取配置信息成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取微信配置失败", e);
            Result result = new Result();
            result.setCode("1");
            result.setMsg("获取配置失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 模拟未关注用户的预约接口（强制返回未关注状态）
     */
    @PostMapping("/mockUnsubscribedAppointment")
    @ApiOperation(value = "模拟未关注用户预约（测试引导关注）")
    public ResponseEntity<Result> mockUnsubscribedAppointment(@RequestBody MockAppointmentRequest request) {
        try {
            Result result = new Result();
            result.setCode("NEED_SUBSCRIBE");
            result.setMsg("预约成功！为了及时接收停车提醒，建议您关注我们的公众号。");

            // 强制返回未关注状态的引导信息
            Map<String, Object> extraData = new HashMap<>();
            extraData.put("qrcode_url", "https://your-domain.com/qrcode.jpg");
            extraData.put("subscribe_tip", "扫描二维码关注公众号，及时接收停车提醒");
            extraData.put("is_subscribed", false);
            extraData.put("simulate_mode", true); // 标识这是模拟模式
            result.setData(extraData);

            log.info("模拟未关注用户预约 - OpenID: {}, 车牌号: {}", request.getOpenid(), request.getPlateNumber());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("模拟未关注用户预约失败", e);
            Result result = new Result();
            result.setCode("1");
            result.setMsg("模拟预约失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 模拟已关注用户的预约接口（强制返回已关注状态）
     */
    @PostMapping("/mockSubscribedAppointment")
    @ApiOperation(value = "模拟已关注用户预约（无需引导）")
    public ResponseEntity<Result> mockSubscribedAppointment(@RequestBody MockAppointmentRequest request) {
        try {
            Result result = new Result();
            result.setCode("0");
            result.setMsg("预约成功！您已关注公众号，将及时收到停车提醒。");

            // 强制返回已关注状态
            Map<String, Object> extraData = new HashMap<>();
            extraData.put("is_subscribed", true);
            extraData.put("simulate_mode", true); // 标识这是模拟模式
            result.setData(extraData);

            log.info("模拟已关注用户预约 - OpenID: {}, 车牌号: {}", request.getOpenid(), request.getPlateNumber());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("模拟已关注用户预约失败", e);
            Result result = new Result();
            result.setCode("1");
            result.setMsg("模拟预约失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
} 