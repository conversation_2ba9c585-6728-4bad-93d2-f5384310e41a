package com.parkingmanage.mapper;

import com.parkingmanage.entity.Appointment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

/**
 <p>
  Mapper 接口
 </p>

 <AUTHOR>
 @since 2022-07-13
*/
public interface AppointmentMapper extends BaseMapper<Appointment> {
    Appointment selectAppointmentByOrderNumber(String orderNumber);
    List<Appointment> visitorList(String openid);
    List<Appointment> managerList(String openid);
    List<Appointment> vehicleQueryList(String openid,String platenumber,String leavedate);
    List<Appointment> subAppointQueryList(String openid, String platenumber, String visitorphone, String visitdateBegin,  String visitdateEnd, String recorddateBegin,String recorddateEnd);
    List<Appointment> auditQueryList(String openid, String platenumber, String visitorphone, String visitdateBegin,  String visitdateEnd,String recorddateBegin, String recorddateEnd);
    List<Appointment> allpage(String community,String plateNumber,String  visitdate,String auditstatus);
    List<Appointment> venuepage(String community,String plateNumber,String arrivedate,String leavedate,String venuestatus);
    List<Appointment> listAppointNoAudit(String community,String ownername,String  recorddate);

    Appointment getByQuery(String enterCarLicenseNumber);

    int updateByCarNumber(String enterCarLicenseNumber, String enterTime);

    int updateLeaveTimeByCarNumber(String enterCarLicenseNumber, String enterTime, String leaveTime);

    List<Appointment> getAppointmentPlateNumber(String plateNumber);

    List<Appointment> subAppointQueryListDuration(String openid, String platenumber, String visitdateBegin, String visitdateEnd, String recorddateBegin, String recorddateEnd, String visitorphone);
    
    List<Appointment> listByPhone(String phone);

    List<Appointment> listByAddress(String community, String building, String units, String floor, String room);
}
