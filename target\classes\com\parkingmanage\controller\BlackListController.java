package com.parkingmanage.controller;


import cn.hutool.core.io.resource.MultiFileResource;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.parkingmanage.common.HttpClientUtil;
import com.parkingmanage.common.Result;
import com.parkingmanage.common.config.AIKEConfig;
import com.parkingmanage.entity.*;
import com.parkingmanage.service.BlackListService;
import com.parkingmanage.service.YardInfoService;
import com.parkingmanage.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@RestController
@RequestMapping("/parking/blackList")
public class BlackListController {

    @Autowired
    private BlackListService blackListService;

    @Autowired
    public AIKEConfig aikeConfig;

    @Resource
    private YardInfoService yardInfoService;

    /**
     * 查询黑名单
     *
     * @param parkCodeList
     * @return
     */
    @ApiOperation("查询黑名单车辆列表")
    @PostMapping("/getParkBlackList")
    public ResponseEntity getParkBlackList(String parkCodeList) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("parkCodeList", Arrays.asList(parkCodeList));
        params.put("pageNum", 1);
        params.put("pageSize", 1000);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getParkBlackList", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("查询黑名单车辆列表")
    @GetMapping("/getParkBlack")
    public ResponseEntity getParkBlack(String parkCodeList, String carCode) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("parkCodeList", Arrays.asList(parkCodeList));
        params.put("pageNum", 1);
        params.put("carCode", carCode);
        System.out.println("params = " + params);
        params.put("pageSize", 1000);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getParkBlackList", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("查询黑名单类型")
    @GetMapping("/getSpecialCarTypeList")
    public ResponseEntity getSpecialCarTypeList(String parkCodeList) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("parkCodeList", Arrays.asList(parkCodeList));
        params.put("pageNum", 1);
        params.put("pageSize", 1000);
        params.put("vipGroupType", 2);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getSpecialCarTypeList", params);
        return ResponseEntity.ok(data);
    }

    /**
     * 添加黑名单
     *
     * @param parkCode
     * @param carCode
     * @param carOwner
     * @param isPermament
     * @param timePeriod
     * @param reason
     * @param specialCarTypeId
     * @return
     */
    @ApiOperation("添加黑名单")
    @GetMapping("/addBlackListCar")
    public ResponseEntity addBlackListCar(String parkCode, String carCode, String carOwner, String isPermament, String timePeriod, String reason, String remark1, String remark2, String specialCarTypeId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("parkCode", parkCode);
        params.put("carCode", carCode);
        params.put("carOwner", carOwner);
        params.put("reason", reason);
        params.put("isPermament", isPermament);
        params.put("specialCarTypeId", specialCarTypeId);
        if (!remark1.isEmpty()) {
            params.put("remark1", remark1);
        } else if (!remark2.isEmpty()) {
            params.put("remark2", remark2);
        }
        params.put("timePeriod", timePeriod);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "addBlackListCar", params);
        return ResponseEntity.ok(data);
    }

    /**
     * 文件批量搭导入改写后添加黑名单
     *
     * @param blackVue
     * @return
     */
    @ApiOperation("文件批量导入改写后添加黑名单")
    @PostMapping("/addBlackListCarVue")
    public ResponseEntity addBlackListCarVue(@RequestBody BlackVue blackVue) {
        ArrayList<String> strings = new ArrayList<>();
        HashMap<String, String> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
//        System.out.println("blackFileVue = " + blackVue);
        params.put("parkCode", blackVue.getParkCode());
        params.put("carCode", blackVue.getCarCode());
        params.put("carOwner", blackVue.getCarOwner());
        params.put("reason", blackVue.getReason());
        if (blackVue.getIsPermament().equals("永久")) {
            params.put("isPermament", "1");
            params.put("timePeriod", "");
        } else if (blackVue.getIsPermament().equals("自定义")) {
            params.put("isPermament", "0");
            params.put("timePeriod", blackVue.getTimePeriod());
        } else {
            params.put("isPermament", blackVue.getIsPermament());
        }
        params.put("specialCarTypeId", blackVue.getSpecialCarTypeId());
        params.put("remark1", blackVue.getRemark1());
        params.put("remark2", blackVue.getRemark2());
//        String get = HttpClientUtil.doGet("http://localhost:8543/parking/blackList/addBlackListCar", params);
        String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/blackList/addBlackListCar", params);
        JSONObject jsonObject = JSONObject.parseObject(get);
//        System.out.println("jsonObject = " + jsonObject);
        JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
//        System.out.println("调用参数：" + jsonObjectData.getString("message"));
        strings.add(blackVue.getCarCode() + "--" + jsonObjectData.getString("message") + "--" + jsonObjectData.getString("status"));
        if (jsonObjectData.getString("message").equals("业务成功")) {
            BlackList blackList = new BlackList();
            blackList.setCarCode(blackVue.getCarCode());
            blackList.setOwner(blackVue.getCarOwner());
            blackList.setParkName(blackVue.getParkName());
            blackList.setRemark1(blackVue.getRemark1());
            blackList.setRemark2(blackVue.getRemark2());
            blackList.setReason(blackVue.getReason());
            if (blackVue.getIsPermament().equals("永久")) {
                blackList.setBlackListForeverFlag("永久");
            } else if (blackVue.getIsPermament().equals("自定义")) {
                blackList.setBlackListForeverFlag(blackVue.getTimePeriod());
            }
            // 根据blackVue.getSpecialCarTypeName()调用查询接口
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("parkCodeList", blackVue.getParkCode());
//            String getSpecialCarType = HttpClientUtil.doGet("http://localhost:8543/parking/blackList/getSpecialCarTypeList", hashMap);
            String getSpecialCarType = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/blackList/getSpecialCarTypeList", hashMap);
            JSONObject jsonObjectSpecialCarType = JSONObject.parseObject(getSpecialCarType);
            // 获取嵌套的JSON对象
            JSONObject dataObject = jsonObjectSpecialCarType.getJSONObject("data").getJSONObject("data");
//            System.out.println("dataObject = " + dataObject);
            // 获取recordList数组
            JSONArray recordList = dataObject.getJSONArray("recordList");
//            System.out.println("recordList = " + recordList.toString());
            // 遍历recordList数组，查找id为502071的对象
            for (int i = 0; i < recordList.size(); i++) {
                JSONObject record = recordList.getJSONObject(i);
                if (record.getString("id").equals(blackVue.getSpecialCarTypeId())) {
                    // 找到匹配的对象，提取name值
                    String name = record.getString("name");
                    blackList.setSpecialCarTypeConfigName(name);
                    break;
                }
            }
            if (blackListService.findOne(blackList).isEmpty()) {
                blackListService.save(blackList);
            }
        }
        Result result = new Result();
        result.setMsg("添加黑名单成功！");
        result.setCode("0");
        result.setData(strings);
        return ResponseEntity.ok(result);
    }


    @ApiOperation("接收前端数据")
    @PostMapping("/addBlackCar")
    public ResponseEntity addBlackCar(@RequestBody BlackVue blackVue) throws ParseException {
//        System.out.println("blackVue = " + blackVue);
        ArrayList<String> strings = new ArrayList<>();
        HashMap<String, String> hashMap = new HashMap<>();
        // 处理数据,将接收到的字符串按照","进行拆分
        String[] split = blackVue.getCarCode().split(",");
        for (String carNo : split) {
            BlackList blackList = new BlackList();
            blackList.setParkName(blackVue.getParkName());
            blackList.setOwner(blackVue.getCarOwner());
            blackList.setCarCode(carNo);
            blackList.setReason(blackVue.getReason());
            blackList.setRemark1(blackVue.getRemark1());
            blackList.setRemark2(blackVue.getRemark2());
            blackList.setSpecialCarTypeConfigName(blackVue.getSpecialCarTypeName());
            hashMap.put("parkCode", blackVue.getParkCode());
            hashMap.put("carCode", carNo);
            hashMap.put("carOwner", blackVue.getCarOwner());
            hashMap.put("reason", blackVue.getReason());
            hashMap.put("remark1", blackVue.getRemark1());
            hashMap.put("remark2", blackVue.getRemark2());
            if (blackVue.getIsPermament().equals("永久")) {
                hashMap.put("isPermament", "1");
                blackList.setBlackListForeverFlag("永久");
            } else if (blackVue.getIsPermament().equals("自定义")) {
                hashMap.put("isPermament", "0");
                hashMap.put("timePeriod", formatDateRange(blackVue.getTimePeriod()));
                blackList.setBlackListForeverFlag(blackVue.getTimePeriod());
            }
            hashMap.put("specialCarTypeId", blackVue.getSpecialCarTypeId());
            String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/blackList/addBlackListCar", hashMap);
//            String get = HttpClientUtil.doGet("http://localhost:8543/parking/blackList/addBlackListCar", hashMap);
            JSONObject jsonObject = JSONObject.parseObject(get);
            JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
            strings.add(carNo + "--" + jsonObjectData.getString("message"));
            if (jsonObjectData.getString("message").equals("业务成功")) {
                if (blackListService.findOne(blackList).isEmpty()) {
                    blackListService.save(blackList);
                }
            }
        }
        Result result = new Result();
        result.setMsg("添加黑名单成功！");
        result.setCode("0");
        result.setData(strings);
        return ResponseEntity.ok(result);
    }

    /**
     * 批量导入黑名单
     *
     * @param file
     * @return
     * @throws IOException
     */
    @ApiOperation("批量导入黑名单数据")
    @PostMapping("/import")
    public ResponseEntity importParkBlackList(MultipartFile file) throws IOException, ParseException {
        Result res = new Result();
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        // 调用接口查询车场名称
        HashMap<String, String> hashMap = new HashMap<>();
        ArrayList<BlackList> listArrayList = new ArrayList<>();
        List<BlackList> blackLists = reader.readAll(BlackList.class);
        ArrayList<BlackList> listArraySingleList = new ArrayList<>();
        // 将表格中的车辆字符串进行拆分，接着将所有的其余属性添加到新的BlackList中
        for (BlackList blackList : blackLists) {
            // 将车牌那一行是否包含","包含的话则进行拆分否则的话执行之前的操作
            if (blackList.getCarCode().contains(",")) {
                // 将车牌号码进行拆分
                String[] carNoList = blackList.getCarCode().split(",");
                for (String carNo : carNoList) {
                    BlackList blackListSplit = new BlackList();
                    blackListSplit.setParkName(blackList.getParkName());
                    blackListSplit.setCarCode(carNo);
                    blackListSplit.setReason(blackList.getReason());
                    blackListSplit.setRemark1(blackList.getRemark1());
                    blackListSplit.setRemark2(blackList.getRemark2());
                    blackListSplit.setBlackListForeverFlag(blackList.getBlackListForeverFlag());
                    blackListSplit.setOwner(blackList.getOwner());
                    blackListSplit.setSpecialCarTypeConfigName(blackList.getSpecialCarTypeConfigName());
                    listArrayList.add(blackListSplit);
                }
            } else {
                listArraySingleList.add(blackList);
            }
        }
        // 将listArrayList的值全部添加到blackLists之中
        listArraySingleList.addAll(listArrayList);
        ArrayList<String> strings = new ArrayList<>();
        for (BlackList blackList : listArraySingleList) {
//            System.out.println("blackList = " + blackList);
            // 判断车场名称，寻找到车场名称下的黑名单类型ID
            if (blackList.getParkName().equals("万象上东")) {
                hashMap.put("parkCode", "2KST9MNP");
                hashMap.put("carCode", blackList.getCarCode());
                hashMap.put("carOwner", blackList.getOwner());
                hashMap.put("reason", blackList.getReason());
                hashMap.put("remark1", blackList.getRemark1());
                hashMap.put("remark2", blackList.getRemark2());
                if (blackList.getBlackListForeverFlag().equals("永久")) {
                    hashMap.put("isPermament", "1");
                } else {
                    hashMap.put("isPermament", "0");
                    // 将字符串格式化去除中间的空格，只保留中间的"-"
                    try {
                        String result = formatDateRange(blackList.getBlackListForeverFlag());
//                        System.out.println("格式化后的日期范围: " + result);
                        hashMap.put("timePeriod", result);
                    } catch (IllegalArgumentException | ParseException e) {
                        System.out.println("错误: " + e.getMessage());
                    }
                }
                hashMap.put("specialCarTypeId", "502071");
//                System.out.println("hashMap = " + hashMap);
                String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/blackList/addBlackListCar", hashMap);
                JSONObject jsonObject = JSONObject.parseObject(get);
                JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
                strings.add(blackList.getCarCode() + "--" + blackList.getParkName() + "--" + formatDateRange(blackList.getBlackListForeverFlag()) + "--" + blackList.getOwner() + "--" + "您已欠费" + "--" + "502071" + "--" + jsonObjectData.getString("message"));
                if (jsonObjectData.getString("message").equals("业务成功")) {
                    if (blackListService.findOne(blackList).isEmpty()) {
                        blackList.setSpecialCarTypeConfigName("您已欠费");
                        blackListService.save(blackList);
                    }
                }
            } else if (blackList.getParkName().equals("四季上东")) {
                hashMap.put("parkCode", "2KUG6XLU");
                hashMap.put("carCode", blackList.getCarCode());
                hashMap.put("carOwner", blackList.getOwner());
                hashMap.put("reason", blackList.getReason());
                hashMap.put("remark1", blackList.getRemark1());
                hashMap.put("remark2", blackList.getRemark2());
                if (blackList.getBlackListForeverFlag().equals("永久")) {
                    hashMap.put("isPermament", "1");
                    hashMap.put("timePeriod", null);
                } else {
                    hashMap.put("isPermament", "0");
                    // 将字符串格式化去除中间的空格，只保留中间的"-"
                    hashMap.put("timePeriod", formatDateRange(blackList.getBlackListForeverFlag()));
                }
                hashMap.put("specialCarTypeId", "1526");
                String get = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/blackList/addBlackListCar", hashMap);
                JSONObject jsonObject = JSONObject.parseObject(get);
                JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
//                System.out.println("jsonObjectData = " + jsonObjectData);
                strings.add(blackList.getCarCode() + "--" + blackList.getParkName() + "--" + formatDateRange(blackList.getBlackListForeverFlag()) + "--" + blackList.getOwner() + "--" + "您已欠费请及时缴费" + "--" + "1526" + "--" + jsonObjectData.getString("message"));
                if (jsonObjectData.getString("message").equals("业务成功")) {
                    if (blackListService.findOne(blackList).isEmpty()) {
                        blackList.setSpecialCarTypeConfigName("您已欠费请及时缴费");
                        blackListService.save(blackList);
                    }
                }
            }
        }
        // 返回数据
        res.setMsg("导入成功！");
        res.setCode("0");
        res.setData(strings);
        return ResponseEntity.ok(res);
    }

    /**
     * 更新同步黑名单信息
     *
     * @param parkName
     * @return
     */
    @ApiOperation("更新同步黑名单信息")
    @GetMapping("/synchroBlack")
    public ResponseEntity synchroBlack(String parkName) {
        // 调用接口查询车场名称
        HashMap<String, String> hashMap = new HashMap<>();
        ArrayList<String> parkCodeLists = new ArrayList<>();
        parkCodeLists.add("2KST9MNP");
        parkCodeLists.add("2KUG6XLU");
        if (parkName.equals("万象上东")) {
            hashMap.put("parkCodeList", "2KST9MNP");
        } else if (parkName.equals("四季上东")) {
            hashMap.put("parkCodeList", "2KUG6XLU");
        }
//        System.out.println("hashMap = " + hashMap);
//        String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/vehicleReservation/getParkInfo", hashMap);
        String get = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/blackList/getParkBlackList", hashMap);
        JSONObject jsonObject = JSONObject.parseObject(get);
        JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
//        System.out.println("jsonObjectData = " + jsonObjectData);
        JSONObject jsonObjectDataData = (JSONObject) jsonObjectData.get("data");
        JSONArray recordList = (JSONArray) jsonObjectDataData.get("recordList");
        List<BlackList> blackLists = new ArrayList<>();
        for (int i1 = 0; i1 < recordList.size(); i1++) {
            BlackList blackList = new BlackList();
            JSONObject record = (JSONObject) recordList.get(i1);
            if (record.getString("blacklistForeverFlag").equals("1")) {
                blackList.setBlackListForeverFlag("永久");
            } else {
                String validFrom = record.getString("validFrom");
                String validTo = record.getString("validTo");
                // 将数据格式化成:yyyy-mm-dd hh:mm:ss
                convertDateFormat(validFrom);
                convertDateFormat(validTo);
                blackList.setBlackListForeverFlag(validFrom + "-" + validTo);
            }
            blackList.setOwner(record.getString("owner"));
            blackList.setCarCode(record.getString("carCode"));
            blackList.setReason(record.getString("reason"));
            blackList.setRemark1(record.getString("remark1"));
            blackList.setRemark2(record.getString("remark2"));
            if (parkName.equals("万象上东")) {
                blackList.setParkName("万象上东");
            } else if (parkName.equals("四季上东")) {
                blackList.setParkName("四季上东");
            }
            blackList.setSpecialCarTypeConfigName(record.getString("specialCarTypeConfigName"));
            blackLists.add(blackList);
        }
        int n = 0;
        for (BlackList blackList : blackLists) {
            if (blackListService.findOne(blackList).isEmpty()) {
                boolean save = blackListService.save(blackList);
                if (save) {
                    n++;
                }
            }
        }
        Result result = new Result();
        result.setMsg("更新成功！");
        result.setCode("0");
        result.setData(n);
        return ResponseEntity.ok(result);
    }

    /**
     * 分页查询
     *
     * @param parkName
     * @param carCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    @ApiOperation("分页查询")
    @GetMapping("/page")
//    public IPage<BlackList> findPage(@RequestParam(required = false) String parkName,
    public IPage<BlackList> findPage(@RequestParam(required = false) String parkName,
                                     @RequestParam(required = false) String carCode,
                                     @RequestParam(required = false) String userName,
                                     @RequestParam(required = false) String specialCarTypeConfigName,
                                     @RequestParam(required = false) String blackReason,
                                     @RequestParam(required = false) String remark1,
                                     @RequestParam(required = false) String remark2,
                                     @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                     @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        // TODO 编写查询逻辑
        List<BlackList> blackListList = blackListService.queryInfoOnly(parkName, carCode, specialCarTypeConfigName, userName, blackReason, remark1, remark2);
        // 或者使用 Java 8 的 lambda 表达式
        blackListList.sort((o1, o2) -> Integer.compare(o2.getId(), o1.getId()));
        return PageUtils.getPage(blackListList, pageNum, pageSize);
    }

    /**
     * 字符串日期格式化
     * @param input
     * @return
     */
    public static String convertDateFormat(String input) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = inputFormat.parse(input);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return outputFormat.format(date);
    }

    /**
     * 格式化日日期
     *
     * @param input
     * @return
     * @throws ParseException
     * @throws IllegalArgumentException
     */
    public static String formatDateRange(String input) throws ParseException, IllegalArgumentException {
        // 去除输入字符串中的不必要空格
        if (input.equals("永久")) {
            return "永久";
        } else {
            input = input.replaceAll("[\\s:-]+", "");
//            System.out.println("input = " + input);
            // 在第15到16字符中间添加"-"
            String result = input.substring(0, 14) + "-" + input.substring(14);
            return result;
        }
    }

    /**
     * 移除黑名单
     *
     * @param parkCode
     * @param carNo
     * @return
     */
    @ApiOperation("移除黑名单")
    @GetMapping("/removeBlackListCar")
    public ResponseEntity removeBlackListCar(String parkCode, String carNo) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("parkCode", parkCode);
        params.put("carNo", carNo);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "removeBlackListCar", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("批量移除黑名单")
    @PostMapping("/batchDelete")
    public ResponseEntity batchDelete(@RequestBody List<String> removeInfo) {
        ArrayList<RemoveInfo> removeInfos = new ArrayList<>();
        ArrayList<Integer> ids = new ArrayList<>();
        for (String s : removeInfo) {
            RemoveInfo info = new RemoveInfo();
            // 将字符串利用"_"进行拆分
            String[] s1 = s.split("_");
            info.setCarNo(s1[0]);
            info.setParkCode(s1[1]);
            Integer id = Integer.parseInt(s1[2]);
            ids.add(id);
            removeInfos.add(info);
        }
        HashMap<String, String> hashMap = new HashMap<>();
        // 遍历removeInfos
        ArrayList<String> strings = new ArrayList<>();
        for (RemoveInfo info : removeInfos) {
            // 查询yardInfo数据库
            String parkCode = yardInfoService.selectParkCode(info.getParkCode());
            //enterTime格式必须是yyyy-MM-dd HH:mm:ss
            hashMap.put("parkCode", parkCode);
            hashMap.put("carNo", info.getCarNo());
//            String get = HttpClientUtil.doGet("http://localhost:8543/parking/blackList/removeBlackListCar", hashMap);
            String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/blackList/removeBlackListCar", hashMap);
            JSONObject jsonObject = JSONObject.parseObject(get);
            JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
//            System.out.println("jsonObjectData = " + jsonObjectData);
            strings.add(info.getCarNo() + "--" + jsonObjectData.getString("message"));
        }
        blackListService.removeByIds(ids);
        Result result = new Result();
        result.setMsg("批量删除成功！");
        result.setCode("0");
        result.setData(strings);
        return ResponseEntity.ok(result);
    }
}

