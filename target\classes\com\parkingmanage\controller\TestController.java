package com.parkingmanage.controller;

import com.parkingmanage.common.Result;
import com.parkingmanage.service.OwnerRoleVerificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于调试外部API调用
 * 
 * <AUTHOR>
 * @since 2024
 */
@RestController
@RequestMapping("/parking/test")
@Api(tags = "测试接口")
public class TestController {
    
    private static final Logger logger = LoggerFactory.getLogger(TestController.class);
    
    @Resource
    private OwnerRoleVerificationService ownerRoleVerificationService;
    
    /**
     * 测试外部API调用
     */
    @ApiOperation("测试外部API调用")
    @GetMapping("/externalAPI")
    public ResponseEntity<Result> testExternalAPI(
            @RequestParam(required = false, defaultValue = "13593527970") String phoneNumber,
            @RequestParam(required = false, defaultValue = "四季上东") String parkName) {
        Result result = new Result();
        
        try {
            logger.info("🧪 开始测试外部API调用，手机号: [{}], 停车场: [{}]", phoneNumber, parkName);
            
            long startTime = System.currentTimeMillis();
            
            // 测试指定停车场的业主身份验证（推荐方式）
            boolean isOwnerInPark = ownerRoleVerificationService.isOwnerByPhoneNumberInPark(phoneNumber, parkName);
            
            long parkTestTime = System.currentTimeMillis();
            
            // 测试所有停车场的业主身份验证（对比用）
            boolean isOwnerAll = ownerRoleVerificationService.isOwnerByPhoneNumber(phoneNumber);
            
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> testResult = new HashMap<>();
            testResult.put("phoneNumber", phoneNumber);
            testResult.put("parkName", parkName);
            testResult.put("isOwnerInPark", isOwnerInPark);
            testResult.put("isOwnerAll", isOwnerAll);
            testResult.put("parkTestDuration", (parkTestTime - startTime) + "ms");
            testResult.put("allTestDuration", (endTime - parkTestTime) + "ms");
            testResult.put("totalDuration", (endTime - startTime) + "ms");
            testResult.put("testTime", new java.util.Date());
            
            if (isOwnerInPark) {
                // 获取指定停车场的详细信息
                Map<String, Object> ownerDetailsInPark = ownerRoleVerificationService.getOwnerDetailsByPark(phoneNumber, parkName);
                testResult.put("ownerDetailsInPark", ownerDetailsInPark);
            }
            
            if (isOwnerAll) {
                // 获取所有停车场的详细信息
                Map<String, Object> ownerDetailsAll = ownerRoleVerificationService.getOwnerDetailsByPhone(phoneNumber);
                testResult.put("ownerDetailsAll", ownerDetailsAll);
            }
            
            // 获取统计信息
            Map<String, Object> statistics = ownerRoleVerificationService.getStatistics();
            testResult.put("statistics", statistics);
            
            // 性能对比分析
            long parkTime = parkTestTime - startTime;
            long allTime = endTime - parkTestTime;
            testResult.put("performanceAnalysis", String.format(
                "指定停车场查询: %dms, 全部停车场查询: %dms, 性能提升: %.1f%%",
                parkTime, allTime, allTime > 0 ? ((double)(allTime - parkTime) / allTime * 100) : 0.0
            ));
            
            result.setData(testResult);
            result.setCode("0");
            result.setMsg("测试完成");
            
            logger.info("✅ 外部API测试完成，指定停车场: {}ms, 全部停车场: {}ms", parkTime, allTime);
            
        } catch (Exception e) {
            logger.error("❌ 外部API测试失败", e);
            result.setCode("1");
            result.setMsg("测试失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试外部API连接性
     */
    @ApiOperation("测试外部API连接性")
    @GetMapping("/connectivity")
    public ResponseEntity<Result> testConnectivity() {
        Result result = new Result();
        
        try {
            logger.info("🧪 开始测试外部API连接性");
            
            Map<String, Object> connectivityResult = new HashMap<>();
            connectivityResult.put("apiUrl", "https://open.yidianting.xin/openydt/api/v3/");
            connectivityResult.put("testTime", new java.util.Date());
            
            // 尝试获取统计信息来测试连接
            long startTime = System.currentTimeMillis();
            Map<String, Object> statistics = ownerRoleVerificationService.getStatistics();
            long endTime = System.currentTimeMillis();
            
            connectivityResult.put("connectionSuccessful", statistics != null && !statistics.isEmpty());
            connectivityResult.put("responseTime", (endTime - startTime) + "ms");
            connectivityResult.put("statistics", statistics);
            
            result.setData(connectivityResult);
            result.setCode("0");
            result.setMsg("连接性测试完成");
            
            logger.info("✅ 外部API连接性测试完成");
            
        } catch (Exception e) {
            logger.error("❌ 外部API连接性测试失败", e);
            result.setCode("1");
            result.setMsg("连接性测试失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
} 