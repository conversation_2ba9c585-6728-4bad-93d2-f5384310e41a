<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.RentalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Rental">
        <id column="order_id" property="orderId"/>
        <result column="customer_id" property="customerId"/>
        <result column="department_id" property="departmentId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_code" property="customerCode"/>
        <result column="customer_address" property="customerAddress"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="telephone" property="telephone"/>
        <result column="rent" property="rent"/>
        <result column="lease_time" property="leaseTime"/>
        <result column="audius_user_id" property="audiusUserId"/>
        <result column="audius_status" property="audiusStatus"/>
        <result column="expected_return" property="expectedReturn"/>
        <result column="remarks" property="remarks"/>
        <result column="actual_return" property="actualReturn"/>
        <result column="deleted" property="deleted"/>
        <result column="applicant_user_id" property="applicantUserId"/>
        <result column="actual_rent" property="actualRent"/>
        <result column="day_price" property="dayPrice"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_id, customer_id, department_id, customer_name, customer_code, customer_address, device_id, device_code, device_name,telephone, rent, lease_time, audius_user_id,audius_status,expected_return, remarks, actual_return, deleted,applicant_user_id,actual_rent,day_price,device_type
    </sql>
    <select id="getByTypeRent" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rental
        where actual_rent is not null and actual_return &gt; DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
    </select>
</mapper>