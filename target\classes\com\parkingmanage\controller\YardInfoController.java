package com.parkingmanage.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.parkingmanage.common.HttpClientUtil;
import com.parkingmanage.common.Result;
import com.parkingmanage.common.config.AIKEConfig;
import com.parkingmanage.entity.YardInfo;
import com.parkingmanage.service.YardInfoService;
import com.parkingmanage.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 <p>
 前端控制器
 * </p>
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/parking/yardInfo")
public class YardInfoController {
    @Resource
    private YardInfoService yardInfoService;

    @Autowired
    public AIKEConfig aikeConfig;

    @ApiOperation("添加")
    @PostMapping
    public ResponseEntity<Result> insertYardInfo(@RequestBody YardInfo yardInfo) {
        int num = yardInfoService.duplicate(yardInfo);
        Result result = new Result();
        if (num == 0) {
            yardInfoService.save(yardInfo);

        } else {
            result.setCode("1");
            result.setMsg("数据重复，增加失败！");
        }

        return ResponseEntity.ok(result);
    }

    @ApiOperation("修改")
    @PutMapping
    public ResponseEntity<Result> update(@RequestBody YardInfo yardInfo) {
        int num = yardInfoService.duplicate(yardInfo);
        Result result = new Result();
        if (num == 0) {
            yardInfoService.updateById(yardInfo);
        } else {
            result.setCode("1");
            result.setMsg("数据重复，修改失败！");
        }
        return ResponseEntity.ok(result);
    }

    @ApiOperation("删除")
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return yardInfoService.removeById(id);
    }

    @ApiOperation("查询所有")
    @GetMapping("/getAllYardInfo")
    public List<YardInfo> getAllYardInfo() {
        List<YardInfo> myquery = yardInfoService.list();
        return myquery;
    }

    @ApiOperation("查询所有车场名称")
    @GetMapping("/expYardName")
    public List<YardInfo> expYardNameList() {
        return yardInfoService.expYardNameList();
    }

    @ApiOperation("查询所有车场编码")
    @GetMapping("/yardCode")
    public List<String> yardCode(@RequestParam(required = false) String yardName) {
        return yardInfoService.yardCode(yardName);
    }
    @ApiOperation("获取授权停车场列表")
    @GetMapping("/getAuthParkCodes")
    public ResponseEntity getAuthParkCodes() {
        HashMap<String, Object> params = new HashMap<>();
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET,"getAuthParkCodes", params);
        return ResponseEntity.ok(data);
    }
    @ApiOperation("查询车场名称")
    @GetMapping("/yardName")
    public List<YardInfo> yardNameList() {
        ArrayList<YardInfo> yardInfos = new ArrayList<>();
        HashMap<String, String> params = new HashMap<>();
        String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/yardInfo/getAuthParkCodes",params);
//        String get = HttpClientUtil.doGet("http://localhost:8543/parking/yardInfo/getAuthParkCodes",params);
//        System.out.println("查询车场名称接口：" + get);
        JSONObject jsonObject = JSONObject.parseObject(get);
        JSONObject jsonObjectData = (JSONObject) jsonObject.get("data");
        JSONObject jsonObjectDataData = (JSONObject) jsonObjectData.get("data");
        JSONArray jsonObjectDataDataparkInfoList = (JSONArray) jsonObjectDataData.get("parkInfoList");
        String parkName = "";
        for (int i = 0; i < jsonObjectDataDataparkInfoList.size(); i++) {
            JSONObject jsonObjectparkInfoList = (JSONObject)jsonObjectDataDataparkInfoList.get(i);
            parkName = (String)jsonObjectparkInfoList.get("parkName");
            String parkCode = (String)jsonObjectparkInfoList.get("parkCode");
            YardInfo yardInfo = new YardInfo();
            yardInfo.setYardName(parkName);
            yardInfo.setYardCode(parkCode);
            yardInfo.setDeleted(0);
            yardInfos.add(yardInfo);
        }
        //TODO 将查询到的结果存储到数据库中
        //先查询一下，若有就直接查询，没有添加
        YardInfo yardInfo = yardInfoService.yardByName(parkName);
        if (yardInfo == null) {
//            System.out.println("车场名称插入数据库" + yardInfo);
            yardInfoService.saveBatch(yardInfos);
        }else {
            yardInfoService.updateBatchById(yardInfos);
        }
//        System.out.println("车场名称列表：" + yardInfos);
        return yardInfos;
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public IPage<YardInfo> findPage(@RequestParam(required = false) String yardName,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        List<YardInfo> yardInfoList = yardInfoService.queryListYardInfo(yardName);
        //按照设备名和申请日期排序
        List<YardInfo> asServices = yardInfoList.stream().sorted(Comparator.comparing(YardInfo::getYardName)).collect(Collectors.toList());
        return PageUtils.getPage(asServices, pageNum, pageSize);
    }
}