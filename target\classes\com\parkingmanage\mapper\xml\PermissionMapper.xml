<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.PermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Permission">
        <id column="id" property="id" />
        <result column="NAME" property="name" />
        <result column="PATH" property="path" />
        <result column="DELETED" property="deleted" />
        <result column="PID" property="pid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, NAME, PATH, DELETED, PID
    </sql>

</mapper>
