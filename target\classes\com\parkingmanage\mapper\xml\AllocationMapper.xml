<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.AllocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Allocation">
        <id column="allocation_id" property="allocationId"/>
        <result column="department_id" property="departmentId"/>
        <result column="after_department_id" property="afterDepartmentId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="applicant_user_id" property="applicantUserId"/>
        <result column="application_time" property="applicationTime"/>
        <result column="application_reason" property="applicationReason"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="auditus_time" property="auditusTime"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="remarks" property="remarks"/>
        <result column="deleted" property="deleted"/>
        <result column="file_reason" property="fileReason"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        allocation_id, department_id, after_department_id, device_id, device_code, device_name, applicant_user_id, application_time, application_reason, audit_user_id, auditus_time, audit_status, remarks, deleted, file_reason
    </sql>

</mapper>
