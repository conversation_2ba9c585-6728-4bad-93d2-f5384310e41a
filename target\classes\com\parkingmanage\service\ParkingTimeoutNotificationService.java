package com.parkingmanage.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.parkingmanage.entity.Appointment;
import com.parkingmanage.entity.MessageNotificationLog;
import com.parkingmanage.entity.ParkingTimeoutConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 停车超时提醒服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ParkingTimeoutNotificationService {

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private ParkingTimeoutConfigService parkingTimeoutConfigService;

    @Autowired
    private MessageNotificationLogService messageNotificationLogService;

    @Autowired
    private WechatMessageService wechatMessageService;

    /**
     * 检查并发送超时提醒
     */
    public void checkAndSendTimeoutNotifications() {
        log.info("开始检查车辆停车超时提醒");

        try {
            // 1. 获取所有在场的车辆（已进场但未离场）
            List<Appointment> inParkingVehicles = getInParkingVehicles();
            log.info("当前在场车辆数量：{}", inParkingVehicles.size());

            // 2. 筛选出即将超时的车辆（15分钟内）
            List<Appointment> timeoutWarningVehicles = filterTimeoutWarningVehicles(inParkingVehicles);
            log.info("即将超时车辆数量：{}", timeoutWarningVehicles.size());

            // 3. 发送提醒消息
            for (Appointment appointment : timeoutWarningVehicles) {
                try {
                    sendTimeoutWarningMessage(appointment);
                } catch (Exception e) {
                    log.error("发送超时提醒失败，车牌号：{}", appointment.getPlatenumber(), e);
                }
            }

            log.info("完成检查车辆停车超时提醒，共处理{}辆车", timeoutWarningVehicles.size());
        } catch (Exception e) {
            log.error("检查车辆停车超时提醒异常", e);
        }
    }

    /**
     * 获取所有在场的车辆
     */
    private List<Appointment> getInParkingVehicles() {
        try {
            LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(Appointment::getEnterTime)      // 已进场
                       .isNull(Appointment::getLeaveTime)         // 未离场
                       .eq(Appointment::getStatus, 1)             // 状态为进场
                       .eq(Appointment::getTimeoutNotified, 0)    // 未发送过提醒
                       .isNotNull(Appointment::getOpenid);        // 有OpenID

            return appointmentService.list(queryWrapper);
        } catch (Exception e) {
            log.error("获取在场车辆失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 筛选即将超时的车辆（15分钟内）
     */
    private List<Appointment> filterTimeoutWarningVehicles(List<Appointment> appointments) {
        List<Appointment> result = new ArrayList<>();

        for (Appointment appointment : appointments) {
            try {
                // 获取该车场和车辆类型的超时时间配置
                ParkingTimeoutConfig config = parkingTimeoutConfigService.getByYardCodeAndVehicleType(
                    appointment.getYardCode(), appointment.getVehicleType());

                if (config == null || !config.getIsActive()) {
                    log.debug("车牌号{}未找到有效的超时配置", appointment.getPlatenumber());
                    continue;
                }

                // 计算预计离场时间
                LocalDateTime enterTime = appointment.getEnterTime();
                if (enterTime == null) {
                    log.warn("车牌号{}进场时间为空", appointment.getPlatenumber());
                    continue;
                }

                LocalDateTime expectedLeaveTime = enterTime.plusMinutes(config.getTimeoutMinutes());
                
                // 计算距离超时的时间
                LocalDateTime now = LocalDateTime.now();
                long minutesDiff = java.time.Duration.between(now, expectedLeaveTime).toMinutes();

                // 如果在15分钟内即将超时，加入提醒列表
                if (minutesDiff > 0 && minutesDiff <= 15) {
                    appointment.setExpectedLeaveTime(expectedLeaveTime);
                    result.add(appointment);
                    log.info("车牌号{}即将在{}分钟后超时", appointment.getPlatenumber(), minutesDiff);
                }
            } catch (Exception e) {
                log.error("处理车辆{}超时检查异常", appointment.getPlatenumber(), e);
            }
        }

        return result;
    }

    /**
     * 发送超时提醒消息
     */
    private void sendTimeoutWarningMessage(Appointment appointment) {
        String openid = appointment.getOpenid();

        if (StringUtils.isEmpty(openid)) {
            log.warn("车牌号{}缺少OpenID，无法发送提醒", appointment.getPlatenumber());
            return;
        }

        try {
            // 检查用户是否关注公众号
            boolean isSubscribed = wechatMessageService.checkUserSubscription(openid);
            if (!isSubscribed) {
                log.warn("用户{}未关注公众号，无法发送模板消息", openid);
                return;
            }

            // 转换LocalDateTime为Date以适配微信消息服务
            Date expectedLeaveTimeDate = Date.from(appointment.getExpectedLeaveTime().atZone(ZoneId.systemDefault()).toInstant());
            Date enterTimeDate = Date.from(appointment.getEnterTime().atZone(ZoneId.systemDefault()).toInstant());

            // 发送微信模板消息
            boolean success = wechatMessageService.sendTimeoutWarningMessage(
                openid, 
                appointment.getPlatenumber(),
                appointment.getYardName(),
                expectedLeaveTimeDate,
                enterTimeDate
            );

            // 记录发送日志
            MessageNotificationLog notificationLog = new MessageNotificationLog();
            notificationLog.setReservationId(appointment.getId());
            notificationLog.setPlateNumber(appointment.getPlatenumber());
            notificationLog.setOpenid(openid);
            notificationLog.setMessageType("timeout_warning");
            notificationLog.setMessageContent(buildTimeoutWarningMessage(appointment));
            notificationLog.setSendTime(new Date());
            notificationLog.setSendStatus(success ? 1 : 2);
            messageNotificationLogService.save(notificationLog);

            // 更新预约提醒状态
            if (success) {
                appointment.setTimeoutNotified(1);
                appointmentService.updateById(appointment);
                log.info("车牌号{}超时提醒发送成功", appointment.getPlatenumber());
            } else {
                log.warn("车牌号{}超时提醒发送失败", appointment.getPlatenumber());
            }

        } catch (Exception e) {
            log.error("发送超时提醒失败", e);

            // 记录失败日志
            MessageNotificationLog errorLog = new MessageNotificationLog();
            errorLog.setReservationId(appointment.getId());
            errorLog.setPlateNumber(appointment.getPlatenumber());
            errorLog.setOpenid(openid);
            errorLog.setMessageType("timeout_warning");
            errorLog.setSendTime(new Date());
            errorLog.setSendStatus(2);
            errorLog.setErrorMessage(e.getMessage());
            messageNotificationLogService.save(errorLog);
        }
    }

    /**
     * 构造超时提醒消息内容
     */
    private String buildTimeoutWarningMessage(Appointment appointment) {
        SimpleDateFormat sdf = new SimpleDateFormat("MM月dd日 HH:mm");
        Date expectedLeaveTimeDate = Date.from(appointment.getExpectedLeaveTime().atZone(ZoneId.systemDefault()).toInstant());
        return String.format(
            "🚗 停车超时提醒\n\n" +
            "车牌号：%s\n" +
            "停车场：%s\n" +
            "预计离场时间：%s\n\n" +
            "您的车辆将在15分钟内超时，请及时离场！",
            appointment.getPlatenumber(),
            appointment.getYardName(),
            sdf.format(expectedLeaveTimeDate)
        );
    }
} 