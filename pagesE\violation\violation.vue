<template>
	<view class="violation-container">
		<!-- 用户界面 -->
		<view v-if="!isHousekeeper" class="user-view">
			<!-- 信用度展示区 -->
			<view class="credit-score-section">
				<view class="credit-ring">
					<u-circle-progress :percent="creditScore" :width="200" activeColor="#2979ff">
						<view class="credit-inner">
							<text class="credit-num">{{ creditScore }}</text>
							<text class="credit-label">信用分</text>
						</view>
					</u-circle-progress>
				</view>
				<view class="credit-message" v-if="creditScore >= 80">
					<u-icon name="checkmark-circle" color="#19be6b" size="28"></u-icon>
					<text>您的信用记录良好，请继续保持！</text>
				</view>
				<view class="credit-message warning" v-else>
					<u-icon name="warning" color="#ff9900" size="28"></u-icon>
					<text>您的信用记录较差，请注意改善停车行为。</text>
				</view>
			</view>

			<!-- 违规统计概览 -->
			<view class="violation-stats">
				<view class="stats-card">
					<text class="stats-title">本月违规</text>
					<text class="stats-num">{{ monthViolations }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">累计违规</text>
					<text class="stats-num">{{ totalViolations }}</text>
				</view>
				<view class="stats-card">
					<text class="stats-title">最近违规</text>
					<text class="stats-date">{{ lastViolationDate }}</text>
				</view>
			</view>

			<!-- 违规趋势图表 -->
			<view class="chart-section">
				<view class="chart-header">
					<text class="chart-title">违规趋势分析</text>
					<u-tabs :list="timeRanges" @click="changeTimeRange"></u-tabs>
				</view>
				<qiun-data-charts type="column" :opts="chartOpts" :chartData="chartData" />
			</view>
			<!-- 违规车辆列表 -->
			<view class="violation-list">
				<view class="list-header">
					<text>违规记录</text>
					<u-tabs :list="filterTabs" @click="changeFilter"></u-tabs>
				</view>
				<view class="list-content">
					<view v-for="(item, index) in violationList" :key="index" class="violation-item-compact"
						:class="{ 'expanded': item.isExpanded }">
						<!-- 紧凑的一行显示 -->
						<view class="compact-row" @click="toggleViolationItem(index)">
							<view class="main-info">
								<!-- 车牌号 -->
								<text class="plate-number-compact"
									:class="{ 'blue-plate': !item.isNewEnergy, 'green-plate': item.isNewEnergy }">
									{{ item.plateNumber }}
								</text>

								<!-- 状态标签 -->
								<view class="status-tag" :class="{
									'status-not-processed': item.statusText === '未处理' || item.statusText === '未进场',
									'status-processed': item.statusText === '已处理' || item.statusText === '已离场',
									'status-processing': item.statusText === '处理中' || item.statusText === '在场'
								}">
									<text class="status-text">{{ getStatusShortText(item.statusText) }}</text>
								</view>

								<!-- 违规原因简化显示 -->
								<text class="reason-short">{{ getShortReason(item.reason) }}</text>

								<!-- 时间显示 -->
								<text class="time-display">{{ getRelativeTime(item.appointmentTime) }}</text>
							</view>

							<!-- 展开图标 -->
							<view class="expand-icon">
								<u-icon :name="item.isExpanded ? 'arrow-up' : 'arrow-down'" size="24"
									color="#909399"></u-icon>
							</view>
						</view>

						<!-- 展开后的详细信息 -->
						<view class="detail-content" v-if="item.isExpanded">
							<!-- 紧凑型三行布局 -->
							<view class="compact-detail-layout">
								<!-- 第一行：时间流程 -->
								<view class="time-flow-row">
									<view class="time-item">
										<view class="time-header">
											<text class="time-icon">📅</text>
											<text class="time-label">预约时间</text>
										</view>
										<view class="time-display">
											<text class="time-main">{{ item.appointmentTime ?
												item.appointmentTime.split(' ')[1] : '--:--' }}</text>
											<text class="time-date">{{ item.appointmentTime ?
												item.appointmentTime.split(' ')[0] : '--' }}</text>
										</view>
									</view>
									<view class="time-item" v-if="item.enterTime">
										<view class="time-header">
											<text class="time-icon">🚗</text>
											<text class="time-label">进场时间</text>
										</view>
										<view class="time-display">
											<text class="time-main">{{ item.enterTime.split(' ')[1] }}</text>
											<text class="time-date">{{ item.enterTime.split(' ')[0] }}</text>
										</view>
									</view>

									<view class="time-item" v-if="item.leaveTime">
										<view class="time-header">
											<text class="time-icon">🚛</text>
											<text class="time-label">离场时间</text>
										</view>
										<view class="time-display">
											<text class="time-main">{{ item.leaveTime.split(' ')[1] }}</text>
											<text class="time-date">{{ item.leaveTime.split(' ')[0] }}</text>
										</view>
									</view>
									<view class="time-item" v-else-if="item.enterTime">
										<view class="time-header">
											<text class="time-icon">🟢</text>
											<text class="time-label">在场中</text>
										</view>
									</view>
								</view>

								<!-- 第二行：停车时长 -->
								<view class="duration-row">
									<text class="duration-icon">⏱️</text>
									<text class="duration-label">停车时长:</text>
									<text class="duration-value" :class="{
										'duration-short': getParkingDuration(item).includes('分钟') && !getParkingDuration(item).includes('小时'),
										'duration-medium': getParkingDuration(item).includes('1小时') || getParkingDuration(item).includes('2小时'),
										'duration-long': getParkingDuration(item).includes('3小时') || getParkingDuration(item).includes('4小时') || getParkingDuration(item).includes('5小时')
									}">{{ getParkingDuration(item) }}</text>
								</view>

								<!-- 第三行：状态信息 -->
								<view class="status-row">
									<view class="status-item">
										<text class="status-icon">⚠️</text>
										<text class="status-text">{{ item.reason || item.violationType }}</text>
									</view>
									<text class="status-divider">|</text>
									<view class="status-item">
										<text class="status-icon">🚙</text>
										<text class="status-text" :class="{
											'status-not-processed': item.statusText === '未处理' || item.statusText === '未进场',
											'status-processed': item.statusText === '已处理' || item.statusText === '已离场',
											'status-processing': item.statusText === '处理中' || item.statusText === '在场'
										}">{{ item.statusText }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 管家界面 -->
		<view v-else class="housekeeper-view">
			<!-- 智能搜索栏 -->
			<view class="smart-search-container">
				<view class="search-wrapper">
					<!-- 搜索输入框 -->
					<view class="search-input-box"
						:class="{ focused: searchFocused, hasText: searchKeyword.length > 0 }">
						<input class="search-input" :value="searchKeyword" placeholder="搜索车牌号/业主姓名/手机号"
							@focus="handleSearchFocus" @blur="handleSearchBlur" @input="handleSearchInput"
							@confirm="performSearch" :focus="searchFocused" />
						<!-- 清空按钮 -->
						<view class="clear-btn" v-if="searchKeyword.length > 0" @click="clearSearchInput">
							<u-icon name="close-circle-fill" size="22" color="#c8c9cc"></u-icon>
						</view>
					</view>

					<!-- 搜索建议下拉框 -->
					<view class="search-suggestions" v-if="showSuggestions && searchSuggestions.length > 0">
						<scroll-view scroll-y class="suggestions-scroll">
							<view class="suggestion-item" v-for="(suggestion, index) in searchSuggestions" :key="index"
								@click="selectSuggestion(suggestion)">
								<view class="suggestion-icon">
									<text class="icon-emoji">
										{{ suggestion.type === 'plate' ? '🚗' :
											suggestion.type === 'phone' ? '📱' :
												suggestion.type === 'name' ? '👤' :
													suggestion.type === 'violation' ? '⚠️' :
														suggestion.type === 'reason' ? '📝' : '🔍' }}
									</text>
								</view>
								<view class="suggestion-content">
									<text class="suggestion-text">{{ suggestion.text }}</text>
									<text class="suggestion-type">{{ getSuggestionTypeText(suggestion.type) }}</text>
								</view>
								<view class="suggestion-arrow">
									<text class="icon-emoji">➡️</text>
								</view>
							</view>
						</scroll-view>
						<view class="suggestions-footer">
							<text class="footer-text">找到 {{ searchSuggestions.length }} 个匹配项</text>
						</view>
					</view>
				</view>

				<!-- 右侧操作按钮组 -->
				<view class="action-buttons">
					<!-- 搜索历史按钮 -->
					<view class="action-btn history-btn" @click="toggleSearchHistory"
						:class="{ active: showSearchHistory }">
						<view class="btn-content">
							<text class="icon-emoji"
								:style="{ color: showSearchHistory ? '#f5a623' : '#d4922a' }">📜</text>
							<text class="btn-label"
								:style="{ color: showSearchHistory ? '#f5a623' : '#d4922a' }">历史</text>
						</view>
					</view>
					<!-- 筛选按钮 -->
					<view class="action-btn filter-btn" @click="openQuickFilter">
						<view class="btn-content">
							<text class="icon-emoji" style="color: #4caf50">🔽</text>
							<text class="btn-label" style="color: #4caf50">筛选</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 搜索历史面板 -->
			<view class="search-history-panel" v-if="showSearchHistory">
				<view class="history-header">
					<view class="header-title">
						<text class="icon-emoji" style="color: #409eff; margin-right: 6rpx">📜</text>
						<text class="title-text">搜索历史</text>
					</view>
					<view class="header-actions">
						<text class="clear-all" @click="clearSearchHistory">清空</text>
					</view>
				</view>
				<view class="history-content" v-if="searchHistory.length > 0">
					<view class="history-item" v-for="(item, index) in recentSearchHistory" :key="index"
						@click="useHistorySearch(item)">
						<view class="history-icon">
							<text class="icon-emoji">{{ item.type === 'plate' ? '🚗' : item.type === 'phone' ? '📱' :
								item.type === 'name' ? '👤' : '🔍' }}</text>
						</view>
						<text class="history-text">{{ item.keyword }}</text>
						<view class="history-time">{{ formatHistoryTime(item.time) }}</view>
						<view class="history-delete" @click.stop="removeSearchHistory(index)">
							<text class="icon-emoji" style="color: #999">×</text>
						</view>
					</view>
				</view>
				<view class="history-empty" v-else>
					<text class="icon-emoji" style="font-size: 48rpx; color: #e0e3e6">🔍</text>
					<text class="empty-text">暂无搜索历史</text>
				</view>
			</view>

			<!-- 热门搜索标签 -->
			<view class="hot-search-tags" v-if="!searchFocused && !showSearchHistory && searchKeyword.length === 0">
				<view class="tags-header">
					<text class="icon-emoji" style="color: #ff6b35; margin-right: 6rpx">🔥</text>
					<text class="tags-title">热门搜索</text>
				</view>
				<view class="tags-container">
					<view class="hot-tag" v-for="(tag, index) in hotSearchTags" :key="index" @click="useHotSearch(tag)">
						<text class="tag-text">{{ tag.text }}</text>
						<view class="tag-count" v-if="tag.count && tag.count > 0">{{ tag.count }}</view>
					</view>
				</view>
			</view>

			<!-- 违规统计 -->
			<view class="weekly-stats">
				<view class="stats-header">
					<text class="stats-title">违规统计</text>
					<view class="date-picker" @click="showStatisticsCalendar">
						<text class="date-text">{{ statisticsStartDate || getLastWeekDate() }}</text>
						<text class="separator">至</text>
						<text class="date-text">{{ statisticsEndDate || getCurrentDate() }}</text>
						<u-icon name="calendar" size="24" color="#2979ff" style="margin-left: 8rpx;"></u-icon>
					</view>
				</view>

				<!-- 高风险车辆统计 -->
				<view class="high-risk-section">
					<view class="section-header">
						<view class="warning-title">
							<u-icon name="error-circle-fill" color="#ff4d4f" size="36"></u-icon>
							<text class="title-text">高风险车辆预警</text>
							<text class="total-count">{{ highRiskCars.length }}辆</text>
						</view>
					</view>

					<view class="risk-vehicles-list">
						<!-- 高风险车辆空状态 -->
						<view v-if="highRiskCars.length === 0" class="empty-state-container">
							<view class="empty-state-card">
								<view class="empty-icon">
									<text class="icon-emoji">🚗</text>
								</view>
								<view class="empty-content">
									<text class="empty-title">暂无高风险车辆</text>
									<text class="empty-subtitle">
										{{ statisticsStartDate }} 至 {{ statisticsEndDate }} 期间暂无高风险车辆记录
									</text>
								</view>
							</view>
						</view>

						<view v-for="(car, index) in highRiskCars" :key="car.plateNumber" class="risk-vehicle-item"
							:class="{ 'expanded': car.isExpanded }" v-else>
							<!-- 车辆主要信息卡片 -->
							<view class="risk-card" @click="toggleViolationDetails(index)">
								<view class="card-header">
									<view class="plate-info">
										<text class="plate-number"
											:class="car.isNewEnergy ? 'green-plate' : 'blue-plate'">
											{{ car.plateNumber }}
										</text>
										<view class="violation-badge" :class="[violationLevelClass(car.count)]">
											<u-icon name="warning-fill" color="#fff" size="24"></u-icon>
											<text>{{ car.count }}次违规</text>
										</view>
									</view>
									<view class="toggle-icon">
										<u-icon :name="car.isExpanded ? 'arrow-up' : 'arrow-down'" size="28"
											color="#909399"></u-icon>
									</view>
								</view>
							</view>

							<!-- 违规详情折叠面板 -->
							<view v-if="car.isExpanded" class="violation-details">
								<!-- 业主信息 -->
								<view class="owner-info-collapsed">
									<view class="info-row">
										<view class="info-item">
											<text class="label name-label">业主姓名</text>
											<text class="value highlight">{{ car.ownerName || '未知' }}</text>
										</view>
										<view class="info-item">
											<text class="label phone-label">联系电话</text>
											<text class="value highlight phone">{{ car.phone || '未知' }}</text>
										</view>
									</view>
									<view class="info-row address-row">
										<text class="label">住址</text>
										<text class="value address">{{ car.address || '未知' }}</text>
									</view>
								</view>

								<!-- 添加状态统计卡片 -->
								<view class="status-stats">
									<view class="stat-card not-entered"
										:class="{ 'selected': car.selectedStatus === 'not-entered' }"
										@click="selectCarStatisticsStatus(car, 'not-entered')">
										<view class="icon-wrapper">
											<u-icon name="info-circle" size="24" color="#fff"></u-icon>
										</view>
										<view class="stat-info">
											<text class="label">未进场</text>
											<text class="count">{{ getStatisticsNotEnteredCount(car) }}次</text>
										</view>
									</view>
									<view class="stat-card in-progress"
										:class="{ 'selected': car.selectedStatus === 'in-progress' }"
										@click="selectCarStatisticsStatus(car, 'in-progress')">
										<view class="icon-wrapper">
											<u-icon name="car" size="24" color="#fff"></u-icon>
										</view>
										<view class="stat-info">
											<text class="label">未离场</text>
											<text class="count">{{ getStatisticsInProgressCount(car) }}次</text>
										</view>
									</view>
									<view class="stat-card has-left"
										:class="{ 'selected': car.selectedStatus === 'has-left' }"
										@click="selectCarStatisticsStatus(car, 'has-left')">
										<view class="icon-wrapper">
											<u-icon name="checkmark-circle" size="24" color="#fff"></u-icon>
										</view>
										<view class="stat-info">
											<text class="label">已离场</text>
											<text class="count">{{ getStatisticsLeftCount(car) }}次</text>
										</view>
									</view>
								</view>

								<view class="violation-summary">
									<view class="summary-item"
										v-for="(count, type) in getViolationTypes(car.violations)" :key="type">
										<text class="type">{{ type }}</text>
										<text class="count">{{ count }}次</text>
									</view>
								</view>

								<!-- 当选择了状态筛选但没有符合条件的记录时显示提示 -->
								<view v-if="car.selectedStatus && car.violations.length === 0" class="no-records-tip">
									<view class="tip-icon">
										<text class="icon-emoji">📋</text>
									</view>
									<text class="tip-text">该车辆在"{{ getStatusDisplayText(car.selectedStatus) }}"状态下暂无违规记录</text>
								</view>

								<view v-for="(violation, vIndex) in car.violations" :key="vIndex"
									class="violation-item">
									<!-- 预约时间和状态在同一行 -->
									<view class="appointment-status-row">
										<view class="appointment-tag">
											<u-icon name="calendar" size="20" color="#2979ff"></u-icon>
											<text class="tag-label">预约时间</text>
											<text class="time">{{ violation.appointmentTime }}</text>
										</view>

										<!-- 状态标识 -->
										<view v-if="!violation.enterTime" class="status-badge not-entered">
											<u-icon name="info-circle" size="20" color="#fff"></u-icon>
											<text>未进场</text>
										</view>
										<view v-if="violation.enterTime && !violation.leaveTime"
											class="status-badge in-progress">
											<u-icon name="car" size="20" color="#fff"></u-icon>
											<text>停车中</text>
										</view>
										<view v-if="violation.leaveTime" class="status-badge has-left">
											<u-icon name="checkmark-circle" size="20" color="#fff"></u-icon>
											<text>已离场</text>
										</view>
									</view>

									<!-- 车辆时间线 -->
									<view class="vehicle-timeline">

										<!-- 进出场时间显示 - 紧凑样式 -->
										<view v-if="violation.enterTime || violation.leaveTime"
											class="timeline-cards compact">
											<view v-if="violation.enterTime" class="timeline-card enter-card">
												<view class="card-icon">
													<text class="emoji-icon">🚗</text>
												</view>
												<view class="card-content">
													<text class="card-label">进场时间</text>
													<view class="card-time">
														<text class="date-part">{{ formatFullDate(violation.enterTime)
															}}</text>
														<text class="time-part">{{ formatTime(violation.enterTime)
															}}</text>
													</view>
												</view>
											</view>
											<view v-if="violation.leaveTime" class="timeline-card leave-card">
												<view class="card-icon">
													<text class="emoji-icon">🚙</text>
												</view>
												<view class="card-content">
													<text class="card-label">离场时间</text>
													<view class="card-time">
														<text class="date-part">{{ formatFullDate(violation.leaveTime)
															}}</text>
														<text class="time-part">{{ formatTime(violation.leaveTime)
															}}</text>
													</view>
												</view>
											</view>
										</view>
									</view>

									<view class="violation-content">
										<!-- 横向紧凑信息 -->
										<view class="info-row">
											<view class="info-tag violation-tag">
												<u-icon name="error-circle" size="16" color="#ff4d4f"></u-icon>
												<text class="tag-label">违规类型</text>
												<text class="tag-value">{{ violation.violationType }}</text>
											</view>
											<view class="info-tag reason-tag">
												<u-icon name="bookmark" size="16" color="#2979ff"></u-icon>
												<text class="tag-label">预约原因</text>
												<text class="tag-value">{{ violation.appointmentReason || '未填写'
													}}</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 实时违规记录 -->
				<view class="realtime-violations">
					<!-- 添加统计卡片组 -->
					<view class="violation-stats-cards">
						<view class="stat-card not-entered"
							:class="{ 'selected': realtimeSelectedStatus === 'not-entered' }"
							@click="selectRealtimeStatus('not-entered')">
							<view class="icon-wrapper">
								<u-icon name="clock" size="32" color="#fff"></u-icon>
							</view>
							<view class="stat-info">
								<text class="count">{{ getRealtimeNotEnteredCount() }}</text>
								<text class="label">未进场</text>
							</view>
						</view>
						<view class="stat-card in-progress"
							:class="{ 'selected': realtimeSelectedStatus === 'in-progress' }"
							@click="selectRealtimeStatus('in-progress')">
							<view class="icon-wrapper">
								<u-icon name="pause-circle" size="32" color="#fff"></u-icon>
							</view>
							<view class="stat-info">
								<text class="count">{{ getRealtimeInProgressCount() }}</text>
								<text class="label">未离场</text>
							</view>
						</view>
						<view class="stat-card has-left" :class="{ 'selected': realtimeSelectedStatus === 'has-left' }"
							@click="selectRealtimeStatus('has-left')">
							<view class="icon-wrapper">
								<u-icon name="checkmark-circle" size="32" color="#fff"></u-icon>
							</view>
							<view class="stat-info">
								<text class="count">{{ getRealtimeLeftCount() }}</text>
								<text class="label">已离场</text>
							</view>
						</view>
					</view>

					<view class="stats-header">
						<text class="stats-title">实时违规记录</text>
						<view class="date-picker" @click="showRealtimeCalendar">
							<text class="date-text">{{ realtimeStartDate || '开始日期' }}</text>
							<text class="separator">至</text>
							<text class="date-text">{{ realtimeEndDate || '结束日期' }}</text>
							<u-icon name="calendar" size="24" color="#2979ff" style="margin-left: 8rpx;"></u-icon>
						</view>
					</view>

					<!-- 添加实时记录的日历选择器 -->
					<u-calendar :show="showRealtimeCalendarPicker" mode="range" :monthNum="12"
						:defaultDate="realtimeCalendarDefaultDate" :minDate="new Date('2025-01-01').getTime()"
						:maxDate="new Date().getTime()" @confirm="onRealtimeCalendarConfirm"
						@close="showRealtimeCalendarPicker = false">
					</u-calendar>
					<view class="violation-cards">
						<!-- 空状态提示 -->
						<view v-if="realtimeRecords.length === 0" class="empty-state-container">
							<view class="empty-state-card">
								<view class="empty-icon">
									<text class="icon-emoji">📋</text>
								</view>
								<view class="empty-content">
									<text class="empty-title">暂无违规记录</text>
									<text class="empty-subtitle" v-if="realtimeSelectedStatus">
										该{{ getStatusDisplayText(realtimeSelectedStatus) }}状态下暂无违规记录
									</text>
									<text class="empty-subtitle" v-else-if="realtimeStartDate && realtimeEndDate">
										{{ realtimeStartDate }} 至 {{ realtimeEndDate }} 期间暂无违规记录
									</text>
									<text class="empty-subtitle" v-else>
										当前时间范围内暂无违规记录
									</text>
								</view>
								<view class="empty-actions">
									<view class="action-btn" @click="clearAllFilters" v-if="realtimeSelectedStatus">
										<text class="btn-text">清除筛选</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 使用 uView 的滑动组件 -->
						<u-swipe-action ref="uSwipeAction" v-else>
							<u-swipe-action-item v-for="(record, index) in realtimeRecords" :key="index"
								:options="swipeOptions" :name="index" @click="handleSwipeAction">

								<view class="realtime-violation-item-compact"
									:class="{ 'expanded': record.isExpanded }">
									<!-- 紧凑的一行显示 -->
									<view class="compact-row" @click="toggleRealtimeViolationItem(index)">
										<view class="main-info">
											<!-- 车牌号 -->
											<text class="plate-number-compact"
												:class="record.isNewEnergy ? 'green-plate' : 'blue-plate'">
												{{ record.plateNumber }}
											</text>

											<!-- 状态标签 -->
											<view class="status-tag" :class="{
												'status-not-processed': getVehicleStatusText(record) === '未进场',
												'status-processed': getVehicleStatusText(record) === '已离场',
												'status-processing': getVehicleStatusText(record) === '在场'
											}">
												<text class="status-text">{{ getVehicleStatusText(record) }}</text>
											</view>

											<!-- 违规原因简化显示 -->
											<text class="reason-short">{{ getShortReason(record.violationType) }}</text>

											<!-- 时间显示 -->
											<text class="time-display">{{ getRelativeTime(record.appointmentTime)
												}}</text>
										</view>

										<!-- 展开图标 -->
										<view class="expand-icon">
											<u-icon :name="record.isExpanded ? 'arrow-up' : 'arrow-down'" size="24"
												color="#909399"></u-icon>
										</view>
									</view>

									<!-- 展开后的详细信息 -->
									<view class="detail-content" v-if="record.isExpanded">
										<!-- 紧凑型三行布局 -->
										<view class="compact-detail-layout">
											<!-- 第一行：时间流程 -->
											<view class="time-flow-row">
												<view class="time-item">
													<view class="time-header">
														<text class="time-icon">📅</text>
														<text class="time-label">预约时间</text>
													</view>
													<view class="time-display">
														<text class="time-main">{{ record.appointmentTime ?
															record.appointmentTime.split(' ')[1] : '--:--' }}</text>
														<text class="time-date">{{ record.appointmentTime ?
															record.appointmentTime.split(' ')[0] : '--' }}</text>
													</view>
												</view>
												<view class="time-item" v-if="record.enterTime">
													<view class="time-header">
														<text class="time-icon">🚗</text>
														<text class="time-label">进场时间</text>
													</view>
													<view class="time-display">
														<text class="time-main">{{ record.enterTime.split(' ')[1]
															}}</text>
														<text class="time-date">{{ record.enterTime.split(' ')[0]
															}}</text>
													</view>
												</view>
												<view class="time-item" v-else>
													<view class="time-header">
														<text class="time-icon">⏳</text>
														<text class="time-label">未入场</text>
													</view>
												</view>
												<view class="time-item" v-if="record.leaveTime">
													<view class="time-header">
														<text class="time-icon">🚛</text>
														<text class="time-label">离场时间</text>
													</view>
													<view class="time-display">
														<text class="time-main">{{ record.leaveTime.split(' ')[1]
															}}</text>
														<text class="time-date">{{ record.leaveTime.split(' ')[0]
															}}</text>
													</view>
												</view>
												<view class="time-item" v-else-if="record.enterTime">
													<view class="time-header">
														<text class="time-icon">🟢</text>
														<text class="time-label">在场中</text>
													</view>
												</view>
											</view>

											<!-- 第二行：停车时长 -->
											<view class="duration-row">
												<text class="duration-icon">⏱️</text>
												<text class="duration-label">停车时长:</text>
												<text class="duration-value" :class="{
													'duration-short': getParkingDuration(record).includes('分钟') && !getParkingDuration(record).includes('小时'),
													'duration-medium': getParkingDuration(record).includes('1小时') || getParkingDuration(record).includes('2小时'),
													'duration-long': getParkingDuration(record).includes('3小时') || getParkingDuration(record).includes('4小时') || getParkingDuration(record).includes('5小时')
												}">{{ getParkingDuration(record) }}</text>
											</view>

											<!-- 第三行：状态信息 -->
											<view class="status-row">
												<view class="status-item">
													<text class="status-icon">⚠️</text>
													<text class="status-text">{{ record.violationType }}</text>
												</view>
												<text class="status-divider">|</text>
												<view class="status-item">
													<text class="status-icon">🚙</text>
													<text class="status-text" :class="{
														'status-not-processed': getVehicleStatusText(record) === '未进场',
														'status-processed': getVehicleStatusText(record) === '已离场',
														'status-processing': getVehicleStatusText(record) === '在场'
													}">{{ getVehicleStatusText(record) }}</text>
												</view>
											</view>
										</view>
									</view>
								</view>
							</u-swipe-action-item>
						</u-swipe-action>
					</view>
				</view>
				<!-- 违规统计日历选择器 -->
			</view>
		</view>

		<!-- 新增违规浮动按钮 (仅管家可见) -->
		<view v-if="isHousekeeper" class="add-violation-fab" @click="openAddViolationModal">
			<view class="fab-icon">
				<u-icon name="plus" size="22" color="#ffffff"></u-icon>
			</view>
		</view>
		<!-- 自定义TabBar -->
		<custom-tabbar :selectedIndex="2" :userRole="currentUserRole" @tabChange="onTabChange">
		</custom-tabbar>
	</view>
</template>

<script>
	import CustomTabbar from '@/components/custom-tabbar.vue'
	import { violationApi, ownerApi, violationTypeApi, apiUtils } from '@/api/violation-api.js'

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				currentUserRole: 'manager', // 默认管家角色
				isHousekeeper: true, // 是否为管家
				creditScore: 85, // 信用分
				monthViolations: 2, // 本月违规次数
				totalViolations: 5, // 累计违规次数
				lastViolationDate: '', // 最近违规日期，将在mounted中设置
				timeRanges: [{
						name: '周'
					},
					{
						name: '月'
					},
					{
						name: '季'
					},
					{
						name: '年'
					}
				],
				filterTabs: [{
						name: '全部'
					},
					{
						name: '未处理'
					},
					{
						name: '已处理'
					}
				],
				chartOpts: {
					color: ['#2979ff'],
					padding: [15, 10, 0, 15],
					enableScroll: true,
					legend: false,
					xAxis: {
						itemCount: 7
					},
					yAxis: {
						gridType: 'dash',
						dashLength: 2
					}
				},
				chartData: {
					categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
					series: [{
						name: '违规次数',
						data: [2, 1, 3, 0, 2, 1, 0]
					}]
				},
				violationList: [], // 将在mounted中初始化
				// 管家界面数据
				weekDateRange: '', // 将在mounted中设置
				highRiskCars: [],
				realtimeRecords: [], // 将在mounted中初始化
				searchKeyword: '',
				// 智能搜索相关数据
				searchFocused: false,
				showSuggestions: false,
				searchSuggestions: [],
				showSearchHistory: false,
				searchHistory: [],
				hotSearchTags: [{
						text: '黑A12345',
						type: 'plate',
						count: 12
					},
					{
						text: '张三',
						type: 'name',
						count: 8
					},
					{
						text: '未按位停车',
						type: 'violation',
						count: 15
					},
					{
						text: '超时停车',
						type: 'violation',
						count: 10
					},
					{
						text: '回家',
						type: 'reason',
						count: 18
					},
					{
						text: '占用残疾人车位',
						type: 'violation',
						count: 7
					},
					{
						text: '本周违规',
						type: 'time',
						count: 6
					}
				],
				startDate: this.getLastWeekDate(),
				endDate: this.getCurrentDate(),
				showCalendarPicker: false,
				calendarDefaultDate: [],
				originalRealtimeRecords: [],
				tempDateRange: null,
				realtimeStartDate: '',
				realtimeEndDate: '',
				showRealtimeCalendarPicker: false,
				realtimeCalendarDefaultDate: [],
				selectedStatus: '', // 选中的状态 - 保留用于实时违规记录
				statisticsSelectedStatus: '', // 新增：违规统计的选中状态
				realtimeSelectedStatus: '', // 新增：实时违规记录的独立状态选择
				filteredRecords: [], // 筛选后的记录

				// 违规统计的时间范围
				statisticsStartDate: '',
				statisticsEndDate: '',
				showStatisticsCalendarPicker: false,
				statisticsCalendarDefaultDate: [],

				// 分别保存两部分的原始数据
				originalStatisticsRecords: [],
				originalRealtimeRecords: [],
				originalHighRiskCars: [], // 原始高风险车辆数据

				// 当前最大日期（用于日历组件）
				currentMaxDate: '',

				// 触摸相关数据
				touchStartX: 0,
				touchStartTime: 0,
				currentSwipeIndex: null,

				// 滑动操作配置
				swipeOptions: [{
					text: '通过',
					style: {
						backgroundColor: '#19be6b',
						borderRadius: '8rpx 0 0 8rpx',
						width: '120rpx',
						height: '100%',
						fontSize: '28rpx',
						fontWeight: 'bold'
					}
				}, {
					text: '拒绝',
					style: {
						backgroundColor: '#ff4d4f',
						width: '120rpx',
						height: '100%',
						fontSize: '28rpx',
						fontWeight: 'bold',
						borderLeft: '2rpx solid #fff'
					}
				}],

				// 新增违规记录相关数据
				showAddViolationModal: false,
				showPlateScanModal: false,
				submitting: false,
				scanResult: '',
				newViolation: {
					plateNumber: '',
					ownerInfo: null,
					type: '',
					customType: '',
					location: '',
					photos: [],
					voiceMemo: null,
					description: ''
				},
				violationTypes: [{
						name: '超时停车',
						value: 'overtime',
						icon: '🚗'
					},
					{
						name: '未按位停车',
						value: 'wrong_position',
						icon: '🅿️'
					},
					{
						name: '占用他人车位',
						value: 'occupy_space',
						icon: '🚫'
					},
					{
						name: '未经授权停车',
						value: 'unauthorized',
						icon: '🔒'
					},
					{
						name: '堵塞通道',
						value: 'block_passage',
						icon: '🚧'
					},
					{
						name: '占用残疾人车位',
						value: 'disabled_space',
						icon: '♿'
					},
					{
						name: '其他',
						value: 'other',
						icon: '➕'
					}
				]
			};
		},
		watch: {
			// 监听搜索关键词变化，确保始终是字符串
			searchKeyword: {
				handler(newVal, oldVal) {
					if (typeof newVal !== 'string') {
						// 直接设置为字符串，避免使用 $nextTick
						const strValue = String(newVal || '');
						if (strValue !== newVal) {
							this.searchKeyword = strValue;
						}
					}
				},
				immediate: false // 避免初始化时的问题
			}
		},
		computed: {
			// 最近搜索历史（限制显示数量）
			recentSearchHistory() {
				return this.searchHistory.slice(0, 10);
			}
		},
		methods: {
			// 生成最近一周的随机日期（仅日期）
			getRandomDateInLastWeek() {
				const now = new Date();
				const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				const randomTime = oneWeekAgo.getTime() + Math.random() * (now.getTime() - oneWeekAgo.getTime());
				const randomDate = new Date(randomTime);
				return this.formatDate(randomDate);
			},

			// 生成最近一周的随机日期时间
			getRandomDateTimeInLastWeek() {
				const now = new Date();
				const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				const randomTime = oneWeekAgo.getTime() + Math.random() * (now.getTime() - oneWeekAgo.getTime());
				const randomDate = new Date(randomTime);
				return this.formatDateTime(randomDate);
			},

			// 格式化日期时间
			formatDateTime(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},

			// 获取最近一周的日期范围
			getLastWeekDateRange() {
				const now = new Date();
				const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				return `${this.formatDate(oneWeekAgo)}-${this.formatDate(now)}`;
			},

			// 生成实时记录数据
			generateRealtimeRecords() {
				const records = [{
						id: 1,
						plateNumber: '黑A12345',
						appointmentReason: '回家',
						violationType: '未按位停车',
						isNewEnergy: false,
						ownerName: '张三',
						phone: '13812345678',
						address: '8栋5单元555室'
					},
					{
						id: 2,
						plateNumber: '黑A34567',
						appointmentReason: '回家',
						violationType: '跨车位停车',
						isNewEnergy: false,
						ownerName: '张三',
						phone: '13812345678',
						address: '8栋5单元555室'
					},
					{
						id: 3,
						plateNumber: '黑A45678',
						appointmentReason: '回家',
						violationType: '占用残疾人车位',
						isNewEnergy: false,
						ownerName: '张三',
						phone: '13812345678',
						address: '8栋5单元555室'
					},
					{
						id: 4,
						isNewEnergy: false,
						ownerName: '张三',
						phone: '13812345678',
						appointmentReason: '回家',
						address: '8栋5单元555室',
						plateNumber: '黑A12345',
						violationType: '堵塞消防通道'
					},
					{
						id: 8,
						ownerName: '黄巢',
						isNewEnergy: false,
						phone: '1372234478',
						appointmentReason: '回家',
						address: '5栋1单元105室',
						plateNumber: '黑B67890',
						violationType: '压线停车'
					},
					{
						id: 9,
						ownerName: '萧燕燕',
						isNewEnergy: false,
						phone: '1382234478',
						appointmentReason: '回家',
						address: '2栋7单元105室',
						plateNumber: '黑A01234',
						violationType: '占用VIP车位'
					},
					{
						id: 10,
						ownerName: '张三',
						isNewEnergy: false,
						phone: '13812345678',
						plateNumber: '黑A12345',
						address: '8栋5单元555室',
						appointmentReason: '回家',
						violationType: '未熄火停车'
					},
					{
						id: 11,
						ownerName: '赵光义',
						isNewEnergy: false,
						phone: '1381234478',
						appointmentReason: '回家',
						address: '25栋7单元155室',
						plateNumber: '黑A24680',
						violationType: '占用卸货区'
					},
					{
						id: 12,
						isNewEnergy: true,
						ownerName: '王小川',
						phone: '13578458574',
						address: '15栋4单元707室',
						plateNumber: '黑AF57913',
						violationType: '超宽停车'
					},
					{
						id: 13,
						isNewEnergy: true,
						ownerName: '王小川',
						phone: '13578458574',
						address: '15栋4单元707室',
						plateNumber: '黑AF57913',
						violationType: '占用绿化带'
					},
					{
						id: 14,
						isNewEnergy: true,
						ownerName: '王小川',
						phone: '13578458574',
						address: '15栋4单元707室',
						plateNumber: '黑AF57913',
						violationType: '遮挡号牌'
					},
					{
						id: 15,
						isNewEnergy: true,
						ownerName: '王小川',
						phone: '13578458574',
						address: '15栋4单元707室',
						plateNumber: '黑AF57913',
						violationType: '遮挡号牌'
					},
					{
						id: 16,
						isNewEnergy: true,
						ownerName: '王小川',
						phone: '13578458574',
						address: '15栋4单元707室',
						plateNumber: '黑AF57913',
						violationType: '遮挡号牌'
					}
				];

				// 为每个记录生成随机时间
				const recordsWithTime = records.map(record => {
					const appointmentTime = this.getRandomDateTimeInLastWeek();
					const appointmentDate = new Date(appointmentTime);

					// 生成进场时间（预约时间后0-30分钟）
					let enterTime = null;
					if (Math.random() > 0.2) { // 80%的概率有进场时间
						const enterDate = new Date(appointmentDate.getTime() + Math.random() * 30 * 60 * 1000);
						enterTime = this.formatDateTime(enterDate);
					}

					// 生成离场时间（进场时间后30分钟-4小时）
					let leaveTime = null;
					if (enterTime && Math.random() > 0.3) { // 70%的概率有离场时间
						const enterDate = new Date(enterTime);
						const leaveDate = new Date(enterDate.getTime() + (30 + Math.random() * 210) * 60 * 1000);
						leaveTime = this.formatDateTime(leaveDate);
					}

					return {
						...record,
						time: appointmentTime,
						appointmentTime: appointmentTime,
						enterTime: enterTime,
						leaveTime: leaveTime,
						isExpanded: false
					};
				});

				// 按预约时间降序排序（预约时间越晚越靠前）
				return recordsWithTime.sort((a, b) => {
					const timeA = new Date(a.appointmentTime).getTime();
					const timeB = new Date(b.appointmentTime).getTime();
					return timeB - timeA; // 降序排序，时间越晚越靠前
				});
			},

			// 初始化随机时间数据（仅用于备用，不生成实时记录）
			initializeRandomTimeData() {
				// 初始化最近违规日期
				this.lastViolationDate = this.getRandomDateInLastWeek();

				// 初始化违规列表（仅用于统计显示的备用数据）
				this.violationList = [{
					plateNumber: '黑A12345',
					status: 'pending',
					statusText: '未处理',
					appointmentTime: this.getRandomDateTimeInLastWeek(),
					enterTime: this.getRandomDateTimeInLastWeek(),
					leaveTime: '',
					reason: '堵塞消防通道',
					isExpanded: false,
					isNewEnergy: false
				}, {
					plateNumber: '黑B67890',
					status: 'processed',
					statusText: '已离场',
					appointmentTime: this.getRandomDateTimeInLastWeek(),
					enterTime: this.getRandomDateTimeInLastWeek(),
					leaveTime: this.getRandomDateTimeInLastWeek(),
					reason: '压线停车',
					isExpanded: false,
					isNewEnergy: false
				}, {
					plateNumber: '黑A01234',
					status: 'processed',
					statusText: '已离场',
					appointmentTime: this.getRandomDateTimeInLastWeek(),
					enterTime: this.getRandomDateTimeInLastWeek(),
					leaveTime: this.getRandomDateTimeInLastWeek(),
					reason: '占用VIP车位',
					isExpanded: false,
					isNewEnergy: false
				}, {
					plateNumber: '黑A12345',
					status: 'processed',
					statusText: '已离场',
					appointmentTime: this.getRandomDateTimeInLastWeek(),
					enterTime: this.getRandomDateTimeInLastWeek(),
					leaveTime: this.getRandomDateTimeInLastWeek(),
					reason: '超时停车',
					isExpanded: false,
					isNewEnergy: false
				}];

				// 初始化周日期范围
				this.weekDateRange = this.getLastWeekDateRange();

				// 使用生成的实时记录作为备用数据
				this.realtimeRecords = this.generateRealtimeRecords();
				this.originalRealtimeRecords = [...this.realtimeRecords];

				console.warn('⚠️ API加载失败，使用备用数据。显示模拟违规记录数据。');
			},

			// 获取用户角色
			getUserRole() {
				try {
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.role) {
						this.currentUserRole = userInfo.role;
						console.log('📱 [管家违规页面] 获取用户角色:', this.currentUserRole);
					} else {
						console.warn('📱 [管家违规页面] 未找到用户角色信息，使用默认角色');
						this.currentUserRole = 'manager';
					}
				} catch (error) {
					console.error('📱 [管家违规页面] 获取用户角色失败:', error);
					this.currentUserRole = 'manager';
				}
			},

			// TabBar切换事件处理
			onTabChange(tabInfo) {
				console.log('📱 [管家违规页面] TabBar切换:', tabInfo);
			},

			changeTimeRange(index) {
				// 切换时间范围
				this.updateChartData(index)
			},
			changeFilter(index) {
				// 切换筛选条件
				this.filterViolations(index)
			},
			updateChartData(timeRange) {
				// 更新图表数据
			},
			filterViolations(filter) {
				// 筛选违规记录
			},
			refreshList() {
				// 刷新实时违规记录
			},
			handleViolation(id) {
				// 处理违规记录
				uni.showToast({
					title: '处理成功',
					icon: 'success'
				});
			},

			// 显示处理确认弹窗
			showHandleDialog(record) {
				uni.showActionSheet({
					itemList: ['标记为已处理', '发送通知', '记录备注', '取消处理'],
					success: (res) => {
						switch (res.tapIndex) {
							case 0:
								this.markAsProcessed(record);
								break;
							case 1:
								this.sendNotification(record);
								break;
							case 2:
								this.addRemark(record);
								break;
							case 3:
								this.cancelProcess(record);
								break;
						}
					},
					fail: () => {
						console.log('用户取消操作');
					}
				});
			},

			// 标记为已处理
			markAsProcessed(record) {
				uni.showModal({
					title: '确认处理',
					content: `是否将车牌 ${record.plateNumber} 的违规记录标记为已处理？`,
					success: (res) => {
						if (res.confirm) {
							// 更新记录状态
							const index = this.realtimeRecords.findIndex(r => r.id === record.id);
							if (index !== -1) {
								this.$set(this.realtimeRecords[index], 'statusText', '已处理');
								this.$set(this.realtimeRecords[index], 'swiped', false); // 关闭滑动状态
							}
							uni.showToast({
								title: '处理成功',
								icon: 'success'
							});
						}
					}
				});
			},

			// 发送通知
			sendNotification(record) {
				uni.showModal({
					title: '发送通知',
					content: `是否向车主 ${record.ownerName} (${record.phone}) 发送违规处理通知？`,
					success: (res) => {
						if (res.confirm) {
							// 模拟发送通知
							uni.showLoading({
								title: '发送中...'
							});
							setTimeout(() => {
								uni.hideLoading();
								uni.showToast({
									title: '通知已发送',
									icon: 'success'
								});
								// 关闭滑动状态
								const index = this.realtimeRecords.findIndex(r => r.id === record.id);
								if (index !== -1) {
									this.$set(this.realtimeRecords[index], 'swiped', false);
								}
							}, 1500);
						}
					}
				});
			},

			// 添加备注
			addRemark(record) {
				uni.showModal({
					title: '添加备注',
					content: '请输入处理备注信息',
					editable: true,
					placeholderText: '请输入备注内容...',
					success: (res) => {
						if (res.confirm && res.content) {
							// 保存备注
							const index = this.realtimeRecords.findIndex(r => r.id === record.id);
							if (index !== -1) {
								this.$set(this.realtimeRecords[index], 'remark', res.content);
								this.$set(this.realtimeRecords[index], 'swiped', false);
							}
							uni.showToast({
								title: '备注已保存',
								icon: 'success'
							});
						}
					}
				});
			},

			// 取消处理
			cancelProcess(record) {
				// 关闭滑动状态
				const index = this.realtimeRecords.findIndex(r => r.id === record.id);
				if (index !== -1) {
					this.$set(this.realtimeRecords[index], 'swiped', false);
				}
			},
			// 检查用户角色
			async checkUserRole() {
				try {
					// 这里应该调用后端 API 获取用户角色
					// 临时模拟数据
					const userInfo = await this.getUserInfo();
					this.isHousekeeper = userInfo.role === 'housekeeper';
				} catch (error) {
					console.error('获取用户角色失败:', error);
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'none'
					});
				}
			},

			// ==================== 新增违规记录相关方法 ====================

			// 跳转到新增违规页面
			openAddViolationModal() {
				uni.navigateTo({
					url: '/pagesE/violation/add-violation'
				});
			},

			// 关闭新增违规弹窗
			closeAddViolationModal() {
				this.showAddViolationModal = false;
				this.resetViolationForm();
			},

			// 重置违规表单
			resetViolationForm() {
				this.newViolation = {
					plateNumber: '',
					ownerInfo: null,
					type: '',
					customType: '',
					location: '',
					photos: [],
					voiceMemo: null,
					description: ''
				};
			},

			// 车牌号变化时查询车主信息
			async onPlateNumberChange(plateNumber) {
				if (plateNumber && plateNumber.length >= 7) {
					try {
						// 模拟查询车主信息
						const ownerInfo = await this.getOwnerInfoByPlate(plateNumber);
						this.newViolation.ownerInfo = ownerInfo;
					} catch (error) {
						console.error('查询车主信息失败:', error);
						this.newViolation.ownerInfo = null;
					}
				} else {
					this.newViolation.ownerInfo = null;
				}
			},

			// 根据车牌号查询车主信息
			async getOwnerInfoByPlate(plateNumber) {
				return new Promise((resolve) => {
					// 模拟API调用
					setTimeout(() => {
						// 模拟数据
						const mockOwners = {
							'黑A12345': {
								name: '张三',
								phone: '138****5678'
							},
							'黑B67890': {
								name: '李四',
								phone: '139****1234'
							},
							'黑C98765': {
								name: '王五',
								phone: '137****9876'
							}
						};
						resolve(mockOwners[plateNumber] || null);
					}, 500);
				});
			},

			// 选择违规类型
			selectViolationType(type) {
				this.newViolation.type = type.value;
				if (type.value !== 'other') {
					this.newViolation.customType = '';
				}
			},

			// 获取当前位置
			getCurrentLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						// 模拟根据坐标获取地址
						this.newViolation.location = `A区-${Math.floor(Math.random() * 50) + 1}号车位`;
						uni.showToast({
							title: '定位成功',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '定位失败',
							icon: 'none'
						});
					}
				});
			},

			// 拍照取证
			takePhoto() {
				uni.chooseImage({
					count: 6 - this.newViolation.photos.length,
					sizeType: ['compressed'],
					sourceType: ['camera'],
					success: (res) => {
						this.newViolation.photos.push(...res.tempFilePaths);
					}
				});
			},

			// 预览照片
			previewPhoto(index) {
				uni.previewImage({
					urls: this.newViolation.photos,
					current: index
				});
			},

			// 删除照片
			deletePhoto(index) {
				this.newViolation.photos.splice(index, 1);
			},

			// 开始录制语音
			startVoiceRecord() {
				const recorderManager = uni.getRecorderManager();

				uni.showModal({
					title: '录制语音备注',
					content: '点击确定开始录制，录制完成后会自动停止',
					success: (res) => {
						if (res.confirm) {
							recorderManager.start({
								duration: 60000, // 最长60秒
								sampleRate: 16000,
								numberOfChannels: 1,
								encodeBitRate: 96000,
								format: 'mp3'
							});

							// 10秒后自动停止
							setTimeout(() => {
								recorderManager.stop();
							}, 10000);

							recorderManager.onStop((res) => {
								this.newViolation.voiceMemo = res.tempFilePath;
								uni.showToast({
									title: '录制完成',
									icon: 'success'
								});
							});
						}
					}
				});
			},

			// 播放语音
			playVoice() {
				const innerAudioContext = uni.createInnerAudioContext();
				innerAudioContext.src = this.newViolation.voiceMemo;
				innerAudioContext.play();
			},

			// 删除语音
			deleteVoice() {
				this.newViolation.voiceMemo = null;
			},

			// 提交违规记录
			async submitViolation() {
				// 表单验证
				if (!this.newViolation.plateNumber) {
					uni.showToast({
						title: '请输入车牌号',
						icon: 'none'
					});
					return;
				}

				if (!this.newViolation.type) {
					uni.showToast({
						title: '请选择违规类型',
						icon: 'none'
					});
					return;
				}

				if (this.newViolation.type === 'other' && !this.newViolation.customType) {
					uni.showToast({
						title: '请输入自定义违规类型',
						icon: 'none'
					});
					return;
				}

				if (!this.newViolation.location) {
					uni.showToast({
						title: '请输入违规位置',
						icon: 'none'
					});
					return;
				}

				this.submitting = true;

				try {
					// 模拟提交API
					await this.submitViolationToServer();

					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});

					// 关闭弹窗并刷新列表
					this.closeAddViolationModal();
					this.refreshViolationList();

				} catch (error) {
					console.error('提交失败:', error);
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					});
				} finally {
					this.submitting = false;
				}
			},

			// 提交违规记录到服务器
			async submitViolationToServer() {
				return new Promise((resolve) => {
					// 模拟API调用
					setTimeout(() => {
						resolve({
							success: true
						});
					}, 2000);
				});
			},

			// 刷新违规列表
			refreshViolationList() {
				// 重新加载违规记录列表
				this.getInitialData();
			},

			// 打开车牌扫描
			openPlateScanner() {
				this.showPlateScanModal = true;
				this.scanResult = '';
			},

			// 关闭车牌扫描
			closePlateScanner() {
				this.showPlateScanModal = false;
				this.scanResult = '';
			},

			// 开始扫描
			startScan() {
				// 模拟扫描过程
				uni.showLoading({
					title: '识别中...'
				});

				setTimeout(() => {
					uni.hideLoading();
					// 模拟识别结果
					const mockPlates = ['黑A12345', '黑B67890', '黑C98765', '黑D11111'];
					this.scanResult = mockPlates[Math.floor(Math.random() * mockPlates.length)];
				}, 2000);
			},

			// 使用扫描结果
			useScanResult() {
				this.newViolation.plateNumber = this.scanResult;
				this.onPlateNumberChange(this.scanResult);
				this.closePlateScanner();
			},

			// 重新扫描
			rescan() {
				this.scanResult = '';
				this.startScan();
			},

			// 获取用户信息
			getUserInfo() {
				return new Promise((resolve) => {
					// 这里应该是真实的 API 调用
					// 临时返回模拟数据
					setTimeout(() => {
						resolve({
							role: 'user', // 或 'housekeeper'
							userId: '123'
						});
					}, 500);
				});
			},
			// 获取初始数据
			async getInitialData() {
				try {
					// 根据用户角色加载不同的数据
					if (this.isHousekeeper) {
						await this.getHousekeeperData();
					} else {
						await this.getUserViolationData();
					}
				} catch (error) {
					console.error('获取数据失败:', error);
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
				}
			},
			// 获取用户违规数据
			async getUserViolationData() {
				try {
					// 这里应该是实际的 API 调用
					// 临时模拟数据
					// const response = await new Promise(resolve => {
					// 	setTimeout(() => {
					// 		resolve({
					// 			creditScore: 85,
					// 			monthViolations: 2,
					// 			totalViolations: 5,
					// 			lastViolationDate: '2025-03-20',
					// 			violationList: []
					// 		});
					// 	}, 500);
					// });

					// 更新数据
					this.creditScore = response.creditScore;
					this.monthViolations = response.monthViolations;
					this.totalViolations = response.totalViolations;
					this.lastViolationDate = response.lastViolationDate;
					this.violationList = response.violationList;
					// 更新图表数据
					this.updateChartData(0); // 默认显示周数据
				} catch (error) {
					throw error;
				}
			},
			// 获取管家数据
			async getHousekeeperData() {
				try {
					// 获取实时违规记录
					await this.loadRealtimeViolations();

					// 设置日期范围
					this.weekDateRange = this.getLastWeekDateRange();

					// 基于实时违规记录统计高风险车辆
					this.updateStatisticsData();
				} catch (error) {
					console.error('获取管家数据失败:', error);
					// 不重新抛出错误，因为loadRealtimeViolations已经设置了备用数据
					console.log('📋 使用备用数据继续运行，实时记录数量:', this.realtimeRecords.length);

					// 设置日期范围
					this.weekDateRange = this.getLastWeekDateRange();

					// 基于备用数据统计高风险车辆
					this.updateStatisticsData();
				}
			},



			// 加载实时违规记录
			async loadRealtimeViolations() {
				try {
					const params = {
						...apiUtils.formatPagination(1, 50),
						...apiUtils.formatDateRange(
							this.realtimeStartDate,
							this.realtimeEndDate
						)
					};

					if (this.realtimeSelectedStatus && this.realtimeSelectedStatus !== 'all') {
						params.status = this.realtimeSelectedStatus;
					}

					const data = await violationApi.getViolations(params);

					// 转换数据格式
					this.realtimeRecords = data.list.map(item => ({
						id: item.id,
						plateNumber: item.plateNumber,
						violationType: item.violationType,
						appointmentTime: item.appointmentTime,
						enterTime: item.enterTime,
						leaveTime: item.leaveTime,
						status: item.status,
						statusText: item.statusText,
						location: item.location,
						description: item.description,
						ownerName: item.ownerName,
						phone: item.ownerPhone,
						address: item.ownerAddress,
						isNewEnergy: item.isNewEnergy || this.isNewEnergyPlate(item.plateNumber),
						isExpanded: false
					}));

					this.originalRealtimeRecords = [...this.realtimeRecords];
				} catch (error) {
					console.error('加载实时违规记录失败:', error);
					// 使用生成的模拟数据作为备用
					this.realtimeRecords = this.generateRealtimeRecords();
					this.originalRealtimeRecords = [...this.realtimeRecords];
					console.log('📋 使用模拟数据，共', this.realtimeRecords.length, '条违规记录');
					// 不抛出错误，让程序继续运行
				}
			},
			// 智能搜索相关方法
			handleSearchFocus() {
				this.searchFocused = true;
				this.showSearchHistory = false;
				if (this.searchKeyword && typeof this.searchKeyword === 'string' && this.searchKeyword.trim()) {
					this.generateSearchSuggestions(this.searchKeyword);
				}
			},

			handleSearchBlur() {
				// 延迟失焦，避免点击建议项时输入框已失焦
				setTimeout(() => {
					this.searchFocused = false;
					this.showSuggestions = false;
				}, 200);
			},

			handleSearchInput(e) {
				let value = '';

				// 更安全的值提取
				if (e && typeof e === 'object') {
					if (e.detail && typeof e.detail.value !== 'undefined') {
						value = e.detail.value;
					} else if (e.target && typeof e.target.value !== 'undefined') {
						value = e.target.value;
					}
				} else if (typeof e === 'string') {
					value = e;
				}

				// 确保值是字符串
				this.searchKeyword = String(value || '');

				if (this.searchKeyword && this.searchKeyword.trim()) {
					this.generateSearchSuggestions(this.searchKeyword);
					this.showSuggestions = true;
				} else {
					this.showSuggestions = false;
					// 如果搜索框为空，恢复原始数据
					this.restoreOriginalData();
				}
			},

			async generateSearchSuggestions(keyword) {
				if (!keyword || typeof keyword !== 'string') return;

				try {
					// 使用API获取搜索建议
					const suggestions = await ownerApi.getPlateSuggestions(keyword);

					// 转换API返回的数据格式
					this.searchSuggestions = suggestions.map(item => ({
						text: item.plateNumber,
						type: 'plate',
						ownerName: item.ownerName,
						creditScore: item.creditScore,
						isNewEnergy: item.isNewEnergy
					}));

					// 如果API返回的建议较少，补充本地数据
					if (this.searchSuggestions.length < 5) {
						this.addLocalSuggestions(keyword);
					}
				} catch (error) {
					console.error('获取搜索建议失败:', error);
					// 如果API失败，使用本地数据生成建议
					this.generateLocalSuggestions(keyword);
				}
			},

			// 生成本地搜索建议（作为API的备选方案）
			generateLocalSuggestions(keyword) {
				const suggestions = [];
				const searchValue = keyword.toLowerCase().trim();

				// 从高风险车辆中生成建议
				this.originalHighRiskCars.forEach(car => {
					// 车牌号建议
					if (car.plateNumber && car.plateNumber.toLowerCase().includes(searchValue)) {
						const existing = suggestions.find(s => s.text === car.plateNumber && s.type === 'plate');
						if (!existing) {
							suggestions.push({
								text: car.plateNumber,
								type: 'plate'
							});
						}
					}

					// 业主姓名建议
					if (car.ownerName && car.ownerName.toLowerCase().includes(searchValue)) {
						const existing = suggestions.find(s => s.text === car.ownerName && s.type === 'name');
						if (!existing) {
							suggestions.push({
								text: car.ownerName,
								type: 'name'
							});
						}
					}
				});

				// 从实时违规记录中生成建议
				this.originalRealtimeRecords.forEach(record => {
					// 车牌号建议
					if (record.plateNumber && record.plateNumber.toLowerCase().includes(searchValue)) {
						const existing = suggestions.find(s => s.text === record.plateNumber && s.type === 'plate');
						if (!existing) {
							suggestions.push({
								text: record.plateNumber,
								type: 'plate'
							});
						}
					}

					// 违规类型建议
					if (record.violationType && record.violationType.toLowerCase().includes(searchValue)) {
						const existing = suggestions.find(s => s.text === record.violationType && s.type === 'violation');
						if (!existing) {
							suggestions.push({
								text: record.violationType,
								type: 'violation'
							});
						}
					}
				});

				// 限制建议数量并设置
				this.searchSuggestions = suggestions.slice(0, 10);
			},

			// 补充本地建议数据
			addLocalSuggestions(keyword) {
				// 这个方法用于在API返回建议较少时补充本地数据
				this.generateLocalSuggestions(keyword);
			},

			// 获取当前日期
			getCurrentDate() {
				const now = new Date();
				return now.toISOString().split('T')[0];
			},

			// 获取上周日期
			getLastWeekDate() {
				const now = new Date();
				const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				return lastWeek.toISOString().split('T')[0];
			},

			// 获取上周日期范围显示文本
			getLastWeekDateRange() {
				const now = new Date();
				const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					return `${year}.${month}.${day}`;
				};

				return `${formatDate(lastWeek)}-${formatDate(now)}`;
			},

			selectSuggestion(suggestion) {
				this.searchKeyword = String(suggestion.text || '');
				this.showSuggestions = false;
				this.searchFocused = false;

				// 执行搜索
				this.performSearch();

				// 保存到搜索历史
				this.saveSearchHistory(this.searchKeyword, suggestion.type);
			},

			getSuggestionTypeText(type) {
				const typeMap = {
					plate: '车牌号',
					name: '业主姓名',
					phone: '手机号',
					violation: '违规类型',
					reason: '预约原因',
					address: '地址'
				};
				return typeMap[type] || '其他';
			},

			performSearch() {
				if (!this.searchKeyword || typeof this.searchKeyword !== 'string' || !this.searchKeyword.trim()) {
					// 如果搜索框为空，恢复原始数据
					this.restoreOriginalData();
					return;
				}

				// 如果是第一次搜索，保存原始数据
				this.saveOriginalDataIfNeeded();

				// 执行搜索
				const searchValue = this.searchKeyword.toLowerCase().trim();

				// 1. 筛选高风险车辆
				this.highRiskCars = this.originalHighRiskCars.filter(car => {
					// 检查车辆基本信息
					const carMatches = (
						(car.plateNumber && car.plateNumber.toLowerCase().includes(searchValue)) ||
						(car.ownerName && car.ownerName.toLowerCase().includes(searchValue)) ||
						(car.phone && car.phone.includes(searchValue)) ||
						(car.address && car.address.toLowerCase().includes(searchValue))
					);

					// 检查违规记录详情
					const violationMatches = car.violations && car.violations.some(violation => {
						return (
							(violation.violationType && violation.violationType.toLowerCase().includes(
								searchValue)) ||
							(violation.appointmentReason && violation.appointmentReason.toLowerCase()
								.includes(searchValue))
						);
					});

					return carMatches || violationMatches;
				});

				// 2. 筛选实时违规记录
				this.realtimeRecords = this.originalRealtimeRecords.filter(record => {
					return (
						// 搜索车牌号
						(record.plateNumber && record.plateNumber.toLowerCase().includes(searchValue)) ||
						// 搜索业主姓名
						(record.ownerName && record.ownerName.toLowerCase().includes(searchValue)) ||
						// 搜索手机号
						(record.phone && record.phone.includes(searchValue)) ||
						// 搜索违规类型
						(record.violationType && record.violationType.toLowerCase().includes(searchValue)) ||
						// 搜索地址
						(record.address && record.address.toLowerCase().includes(searchValue)) ||
						// 搜索预约原因
						(record.appointmentReason && record.appointmentReason.toLowerCase().includes(
							searchValue))
					);
				}).sort((a, b) => {
					// 按预约时间降序排序（预约时间越晚越靠前）
					const timeA = new Date(a.appointmentTime).getTime();
					const timeB = new Date(b.appointmentTime).getTime();
					return timeB - timeA;
				});

				// 保存到搜索历史
				this.saveSearchHistory(this.searchKeyword, this.detectSearchType(this.searchKeyword));

				// 显示搜索结果统计
				const totalResults = this.highRiskCars.length + this.realtimeRecords.length;
				uni.showToast({
					title: `找到${totalResults}条记录(${this.highRiskCars.length}辆车,${this.realtimeRecords.length}条违规)`,
					icon: 'none',
					duration: 3000
				});
			},

			// 保存原始数据（如果需要）
			saveOriginalDataIfNeeded() {
				if (!this.originalRealtimeRecords.length) {
					this.originalRealtimeRecords = [...this.realtimeRecords];
				}
				if (!this.originalHighRiskCars.length) {
					this.originalHighRiskCars = [...this.highRiskCars];
				}
			},

			// 恢复原始数据
			restoreOriginalData() {
				if (this.originalRealtimeRecords.length) {
					this.realtimeRecords = [...this.originalRealtimeRecords].sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});
				}
				if (this.originalHighRiskCars.length) {
					this.highRiskCars = [...this.originalHighRiskCars];
				}
			},

			clearSearch() {
				this.searchKeyword = '';
				this.showSuggestions = false;
				this.showSearchHistory = false;
				this.searchFocused = false;
				// 恢复原始数据
				this.restoreOriginalData();
				// 强制更新组件
				this.$forceUpdate();
			},

			detectSearchType(keyword) {
				// 检测搜索关键词类型
				if (/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]?$/.test(keyword)) {
					return 'plate';
				}
				if (/^1[3-9]\d{9}$/.test(keyword)) {
					return 'phone';
				}
				if (/^[\u4e00-\u9fa5]{2,4}$/.test(keyword)) {
					return 'name';
				}
				return 'other';
			},

			toggleSearchHistory() {
				this.showSearchHistory = !this.showSearchHistory;
				if (this.showSearchHistory) {
					this.showSuggestions = false;
					this.searchFocused = false;
				}
			},

			saveSearchHistory(keyword, type) {
				// 避免重复添加
				const existingIndex = this.searchHistory.findIndex(item => item.keyword === keyword);
				if (existingIndex !== -1) {
					// 更新时间并移到最前面
					this.searchHistory.splice(existingIndex, 1);
				}

				// 添加到历史记录开头
				this.searchHistory.unshift({
					keyword,
					type,
					time: Date.now()
				});

				// 限制历史记录数量
				if (this.searchHistory.length > 20) {
					this.searchHistory = this.searchHistory.slice(0, 20);
				}

				// 保存到本地存储
				try {
					uni.setStorageSync('violation_search_history', this.searchHistory);
				} catch (e) {
					console.error('保存搜索历史失败:', e);
				}

				// 更新热门搜索标签
				this.updateHotSearchTags();
			},

			loadSearchHistory() {
				try {
					const history = uni.getStorageSync('violation_search_history');
					if (history && Array.isArray(history)) {
						this.searchHistory = history;
						// 加载搜索历史后更新热门搜索标签
						this.updateHotSearchTags();
					}
				} catch (e) {
					console.error('加载搜索历史失败:', e);
				}
			},

			clearSearchHistory() {
				this.searchHistory = [];
				try {
					uni.removeStorageSync('violation_search_history');
				} catch (e) {
					console.error('清空搜索历史失败:', e);
				}
				// 清空搜索历史后重置热门搜索为默认值
				this.updateHotSearchTags();
				uni.showToast({
					title: '已清空搜索历史',
					icon: 'success'
				});
			},

			useHistorySearch(item) {
				this.searchKeyword = String(item.keyword || '');
				this.showSearchHistory = false;
				this.performSearch();
			},

			removeSearchHistory(index) {
				this.searchHistory.splice(index, 1);
				try {
					uni.setStorageSync('violation_search_history', this.searchHistory);
				} catch (e) {
					console.error('保存搜索历史失败:', e);
				}
			},

			formatHistoryTime(timestamp) {
				const now = Date.now();
				const diff = now - timestamp;
				const minutes = Math.floor(diff / (1000 * 60));
				const hours = Math.floor(diff / (1000 * 60 * 60));
				const days = Math.floor(diff / (1000 * 60 * 60 * 24));

				if (minutes < 1) return '刚刚';
				if (minutes < 60) return `${minutes}分钟前`;
				if (hours < 24) return `${hours}小时前`;
				if (days < 7) return `${days}天前`;

				const date = new Date(timestamp);
				return `${date.getMonth() + 1}/${date.getDate()}`;
			},

			useHotSearch(tag) {
				this.searchKeyword = String(tag.text || '');
				this.performSearch();
				// 使用热门搜索时也保存到搜索历史
				this.saveSearchHistory(this.searchKeyword, tag.type);
			},

			// 清空搜索输入框
			clearSearchInput() {
				this.searchKeyword = '';
				this.showSuggestions = false;
				this.searchFocused = false;
				// 恢复原始数据
				this.restoreOriginalData();
			},

			// 更新热门搜索标签
			updateHotSearchTags() {
				// 统计搜索历史中各关键词的搜索次数
				const searchCounts = {};

				this.searchHistory.forEach(item => {
					const key = item.keyword;
					if (searchCounts[key]) {
						searchCounts[key].count++;
						// 更新最新搜索时间
						if (item.time > searchCounts[key].lastTime) {
							searchCounts[key].lastTime = item.time;
							searchCounts[key].type = item.type;
						}
					} else {
						searchCounts[key] = {
							text: key,
							type: item.type,
							count: 1,
							lastTime: item.time
						};
					}
				});

				// 转换为数组并按搜索次数和时间排序
				const hotSearchArray = Object.values(searchCounts)
					.filter(item => item.count >= 1) // 至少搜索过1次
					.sort((a, b) => {
						// 首先按搜索次数降序排序
						if (b.count !== a.count) {
							return b.count - a.count;
						}
						// 搜索次数相同时，按最新搜索时间降序排序
						return b.lastTime - a.lastTime;
					})
					.slice(0, 8); // 限制显示8个热门搜索

				// 如果动态生成的热门搜索不足8个，补充一些默认的热门搜索
				const defaultHotSearch = [{
						text: '未按位停车',
						type: 'violation',
						count: 0
					},
					{
						text: '超时停车',
						type: 'violation',
						count: 0
					},
					{
						text: '占用残疾人车位',
						type: 'violation',
						count: 0
					},
					{
						text: '回家',
						type: 'reason',
						count: 0
					},
					{
						text: '压线停车',
						type: 'violation',
						count: 0
					},
					{
						text: '堵塞消防通道',
						type: 'violation',
						count: 0
					},
					{
						text: '本周违规',
						type: 'time',
						count: 0
					},
					{
						text: '今日违规',
						type: 'time',
						count: 0
					}
				];

				// 补充默认热门搜索（排除已存在的）
				const existingTexts = hotSearchArray.map(item => item.text);
				const additionalHotSearch = defaultHotSearch
					.filter(item => !existingTexts.includes(item.text))
					.slice(0, 8 - hotSearchArray.length);

				// 合并动态生成的和默认的热门搜索
				this.hotSearchTags = [...hotSearchArray, ...additionalHotSearch];

				console.log('🔥 [热门搜索] 更新热门搜索标签:', this.hotSearchTags);
			},

			openQuickFilter() {
				uni.showActionSheet({
					itemList: ['全部记录', '未进场', '未离场', '已离场', '今日违规', '本周违规'],
					success: (res) => {
						switch (res.tapIndex) {
							case 0:
								this.clearAllFilters();
								break;
							case 1:
								this.selectRealtimeStatus('not-entered');
								break;
							case 2:
								this.selectRealtimeStatus('in-progress');
								break;
							case 3:
								this.selectRealtimeStatus('has-left');
								break;
							case 4:
								this.filterByToday();
								break;
							case 5:
								this.filterByThisWeek();
								break;
						}
					}
				});
			},

			clearAllFilters() {
				this.realtimeSelectedStatus = '';
				this.statisticsSelectedStatus = '';
				this.searchKeyword = '';
				this.showSuggestions = false;
				this.showSearchHistory = false;
				this.searchFocused = false;
				// 恢复所有原始数据
				this.restoreOriginalData();
				uni.showToast({
					title: '已清除所有筛选',
					icon: 'success'
				});
			},

			filterByToday() {
				const today = new Date();
				const todayStr = this.formatDate(today);

				if (this.originalRealtimeRecords.length) {
					this.realtimeRecords = this.originalRealtimeRecords.filter(record => {
						const appointmentDate = record.appointmentTime ? record.appointmentTime.split(' ')[0] : '';
						return appointmentDate === todayStr;
					}).sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});
				}

				uni.showToast({
					title: `今日违规${this.realtimeRecords.length}条`,
					icon: 'none'
				});
			},

			filterByThisWeek() {
				const now = new Date();
				const startOfWeek = new Date(now);
				startOfWeek.setDate(now.getDate() - now.getDay());
				startOfWeek.setHours(0, 0, 0, 0);

				if (this.originalRealtimeRecords.length) {
					this.realtimeRecords = this.originalRealtimeRecords.filter(record => {
						const appointmentTime = new Date(record.appointmentTime);
						return appointmentTime >= startOfWeek;
					}).sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});
				}

				uni.showToast({
					title: `本周违规${this.realtimeRecords.length}条`,
					icon: 'none'
				});
			},
			resetSearch() {
				this.searchKeyword = '';
				if (this.originalRealtimeRecords.length) {
					this.realtimeRecords = [...this.originalRealtimeRecords].sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});
				}
			},
			showCalendar() {
				// 如果已经有选择的日期范围，就使用已选择的；否则使用默认的最近7天
				const endDate = this.endDate || this.getCurrentDate();
				const startDate = this.startDate || this.getLastWeekDate();
				this.calendarDefaultDate = [startDate, endDate];
				this.showCalendarPicker = true;
			},
			onCalendarConfirm(e) {
				if (e) {
					this.startDate = e[0];
					this.endDate = e[e.length - 1];
					this.showCalendarPicker = false;
					// 更新数据
					this.updateDataByDateRange();
				} else {
					uni.showToast({
						title: '请选择日期范围',
						icon: 'none'
					});
				}
			},
			toggleViolationDetails(index) {
				this.highRiskCars[index].isExpanded = !this.highRiskCars[index].isExpanded;
			},

			// 切换违规记录展开/折叠状态
			toggleViolationItem(index) {
				this.$set(this.violationList[index], 'isExpanded', !this.violationList[index].isExpanded);
			},

			// 切换实时违规记录展开/折叠状态
			toggleRealtimeViolationItem(index) {
				this.$set(this.realtimeRecords[index], 'isExpanded', !this.realtimeRecords[index].isExpanded);
			},



			// 获取状态简短文本
			getStatusShortText(statusText) {
				switch (statusText) {
					case '未处理':
						return '未进场';
					case '已处理':
						return '已离场';
					case '处理中':
						return '在场';
					default:
						return statusText;
				}
			},

			// 获取简化的违规原因
			getShortReason(reason) {
				if (!reason) return '未知';
				// 简化常见的违规原因
				const reasonMap = {
					'堵塞消防通道': '消防通道',
					'压线停车': '压线停车',
					'占用VIP车位': 'VIP车位',
					'超时停车': '超时停车',
					'未按规定停车': '违规停车'
				};
				return reasonMap[reason] || (reason.length > 6 ? reason.substring(0, 6) + '...' : reason);
			},

			// 获取相对时间显示
			getRelativeTime(timeStr) {
				if (!timeStr) return '';

				const now = new Date();
				const targetTime = new Date(timeStr);
				const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				const targetDate = new Date(targetTime.getFullYear(), targetTime.getMonth(), targetTime.getDate());

				const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24));
				const timeOnly = timeStr.split(' ')[1] || '';

				if (diffDays === 0) {
					return `今天 ${timeOnly}`;
				} else if (diffDays === -1) {
					return `昨天 ${timeOnly}`;
				} else if (diffDays === 1) {
					return `明天 ${timeOnly}`;
				} else if (diffDays > 1 && diffDays <= 7) {
					return `${diffDays}天后 ${timeOnly}`;
				} else if (diffDays < -1 && diffDays >= -7) {
					return `${Math.abs(diffDays)}天前 ${timeOnly}`;
				} else {
					return timeStr.split(' ')[0]; // 显示日期
				}
			},

			// 计算停车时长
			getParkingDuration(record) {
				if (!record) return '';

				// 如果有离场时间，计算停车时长
				if (record.enterTime && record.leaveTime) {
					const enterTime = new Date(record.enterTime);
					const leaveTime = new Date(record.leaveTime);
					const diffMs = leaveTime.getTime() - enterTime.getTime();
					return this.formatDuration(diffMs);
				}
				// 如果只有进场时间，计算当前停车时长
				else if (record.enterTime && !record.leaveTime) {
					const enterTime = new Date(record.enterTime);
					const now = new Date();
					const diffMs = now.getTime() - enterTime.getTime();
					return this.formatDuration(diffMs) + ' (在场)';
				}
				// 如果未入场，计算等待时长
				else if (!record.enterTime) {
					const appointmentTime = new Date(record.appointmentTime);
					const now = new Date();
					const diffMs = now.getTime() - appointmentTime.getTime();
					return '等待 ' + this.formatDuration(diffMs);
				}

				return '';
			},

			// 格式化时长显示
			formatDuration(milliseconds) {
				if (milliseconds < 0) return '0分钟';

				const hours = Math.floor(milliseconds / (1000 * 60 * 60));
				const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

				if (hours > 0) {
					return `${hours}小时${minutes}分钟`;
				} else {
					return `${minutes}分钟`;
				}
			},

			// 获取当前日期
			getCurrentDate() {
				const date = new Date();
				const result = this.formatDate(date);
				console.log('getCurrentDate 方法调用 - 当前时间：', result);
				console.log('getCurrentDate 方法调用 - Date对象：', date);
				return result;
			},

			// 获取一周前的日期
			getLastWeekDate() {
				const date = new Date();
				date.setDate(date.getDate() - 7);
				const result = this.formatDate(date);
				console.log('getLastWeekDate 方法调用 - 一周前：', result);
				return result;
			},

			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const result = `${year}-${month}-${day}`;
				console.log('formatDate 方法调用 - 输入：', date, '输出：', result);
				return result;
			},

			// 更新日期范围内的数据
			updateDataByDateRange() {
				try {
					// 使用完整的日期时间进行比较
					const startTime = new Date(this.startDate + ' 00:00:00').getTime();
					const endTime = new Date(this.endDate + ' 23:59:59').getTime();

					// 创建一个Map来统计日期范围内每辆车的违规次数和信息
					const violationStats = new Map();

					// 遍历实时违规记录，只统计在选定日期范围内的记录
					this.realtimeRecords.forEach(record => {
						// 使用预约时间进行筛选
						const appointmentTime = new Date(record.appointmentTime).getTime();

						// 检查预约时间是否在选定范围内
						if (appointmentTime >= startTime && appointmentTime <= endTime) {
							const plateNumber = record.plateNumber;

							if (!violationStats.has(plateNumber)) {
								// 初始化该车辆的统计信息
								violationStats.set(plateNumber, {
									plateNumber,
									count: 0,
									ownerName: record.ownerName || '',
									phone: record.phone || '',
									address: record.address || '',
									isNewEnergy: this.isNewEnergyPlate(plateNumber),
									isExpanded: false,
									violations: []
								});
							}

							const carStats = violationStats.get(plateNumber);
							carStats.count++;

							// 更新车主信息
							if (record.ownerName) carStats.ownerName = record.ownerName;
							if (record.phone) carStats.phone = record.phone;
							if (record.address) carStats.address = record.address;

							// 添加违规记录
							carStats.violations.push({
								time: record.time,
								violationType: record.violationType,
								status: record.status,
								appointmentReason: record.appointmentReason,
								enterTime: record.enterTime,
								leaveTime: record.leaveTime,
								appointmentTime: record.appointmentTime
							});
						}
					});

					// 筛选出违规次数超过3次的车辆并按违规次数降序排序
					this.highRiskCars = Array.from(violationStats.values())
						.filter(car => car.count >= 3)
						.sort((a, b) => b.count - a.count);

					// 更新实时违规记录显示
					if (this.originalRealtimeRecords.length) {
						this.realtimeRecords = this.originalRealtimeRecords.filter(record => {
							const appointmentTime = new Date(record.appointmentTime).getTime();
							return appointmentTime >= startTime && appointmentTime <= endTime;
						}).sort((a, b) => {
							// 按预约时间降序排序（预约时间越晚越靠前）
							const timeA = new Date(a.appointmentTime).getTime();
							const timeB = new Date(b.appointmentTime).getTime();
							return timeB - timeA;
						});
					}

					// 显示筛选结果提示
					uni.showToast({
						title: `已筛选出${this.highRiskCars.length}辆高风险车辆`,
						icon: 'none',
						duration: 2000
					});

				} catch (error) {
					console.error('更新日期范围数据失败:', error);
					uni.showToast({
						title: '筛选数据失败',
						icon: 'none'
					});
				}
			},
			isNewEnergyPlate(plateNumber) {
				// 简化判断逻辑，只需要检查长度是否为8位
				return plateNumber.length === 8;
			},
			getViolationLevel(count) {
				if (count <= 2) {
					return 'violation-normal'; // 1-2次违规
				} else if (count <= 4) {
					return 'violation-warning'; // 3-4次违规
				} else {
					return 'violation-severe'; // 5次及以上违规
				}
			},
			getViolationLevelClass(count) {
				if (count <= 2) {
					return 'violation-normal'; // 1-2次违规
				} else if (count <= 4) {
					return 'violation-warning'; // 3-4次违规
				} else {
					return 'violation-severe'; // 5次及以上违规
				}
			},
			getViolationTypes(violations) {
				const types = {};
				violations.forEach(violation => {
					if (types[violation.violationType]) {
						types[violation.violationType]++;
					} else {
						types[violation.violationType] = 1;
					}
				});
				return types;
			},
			violationLevelClass(count) {
				if (count <= 2) {
					return 'violation-normal'; // 1-2次违规
				} else if (count <= 4) {
					return 'violation-warning'; // 3-4次违规
				} else {
					return 'violation-severe'; // 5次及以上违规
				}
			},
			// 处理并获取高风险车辆数据

			// 获取车辆状态样式类
			getVehicleStatusClass(record) {
				if (!record.enterTime) return 'not-entered';
				if (record.enterTime && !record.leaveTime) return 'not-left';
				return 'entered';
			},

			// 获取车辆状态图标
			getVehicleStatusIcon(record) {
				if (!record.enterTime) return 'info-circle';
				if (record.enterTime && !record.leaveTime) return 'car';
				return 'checkmark-circle';
			},

			// 获取车辆状态文本
			getVehicleStatusText(record) {
				// 确保 record 存在
				if (!record) return '未知状态';

				// 检查进场时间
				if (!record.enterTime || record.enterTime === null || record.enterTime === undefined) {
					return '未进场';
				}

				// 检查离场时间
				if (!record.leaveTime || record.leaveTime === null || record.leaveTime === undefined) {
					return '在场';
				}

				return '已离场';
			},

			// 获取状态显示文本（用于空状态提示）
			getStatusDisplayText(status) {
				switch (status) {
					case 'not-entered':
						return '未进场';
					case 'in-progress':
						return '在场';
					case 'has-left':
						return '已离场';
					default:
						return '全部';
				}
			},

			// 清除所有筛选条件
			clearAllFilters() {
				// 清除状态筛选
				this.realtimeSelectedStatus = '';

				// 恢复到原始数据
				this.updateRealtimeRecords();

				uni.showToast({
					title: '已清除筛选条件',
					icon: 'success',
					duration: 1500
				});
			},

			// 获取车辆状态颜色
			getVehicleStatusColor(record) {
				if (!record.enterTime) return '#fff';
				if (record.enterTime && !record.leaveTime) return '#fff';
				return '#fff';
			},

			// 格式化日期部分
			formatDate(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			},

			// 格式化时间部分
			formatTime(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
			},

			// 格式化完整日期
			formatFullDate(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},

			// 获取未进场数量
			getNotEnteredCount(violations = null) {
				// 如果传入了特定的violations数组，使用它
				if (violations && Array.isArray(violations)) {
					return violations.filter(v => !v.enterTime).length;
				}

				// 对于违规统计部分，基于原始数据计算
				// 对于实时记录部分，也基于原始数据计算，不受状态筛选影响
				if (!this.originalRealtimeRecords || !Array.isArray(this.originalRealtimeRecords)) {
					return 0;
				}
				return this.originalRealtimeRecords.filter(v => !v.enterTime).length;
			},

			// 获取在场数量
			getInProgressCount(violations = null) {
				// 如果传入了特定的violations数组，使用它
				if (violations && Array.isArray(violations)) {
					return violations.filter(v => v.enterTime && !v.leaveTime).length;
				}

				// 对于违规统计部分，基于原始数据计算
				// 对于实时记录部分，也基于原始数据计算，不受状态筛选影响
				if (!this.originalRealtimeRecords || !Array.isArray(this.originalRealtimeRecords)) {
					return 0;
				}
				return this.originalRealtimeRecords.filter(v => v.enterTime && !v.leaveTime).length;
			},

			// 获取已离场数量
			getLeftCount(violations = null) {
				// 如果传入了特定的violations数组，使用它
				if (violations && Array.isArray(violations)) {
					return violations.filter(v => v.enterTime && v.leaveTime).length;
				}

				// 对于违规统计部分，基于原始数据计算
				// 对于实时记录部分，也基于原始数据计算，不受状态筛选影响
				if (!this.originalRealtimeRecords || !Array.isArray(this.originalRealtimeRecords)) {
					return 0;
				}
				return this.originalRealtimeRecords.filter(v => v.enterTime && v.leaveTime).length;
			},

			// 实时记录区域专用的计数方法 - 基于当前时间范围进行统计，不受状态筛选影响
			getRealtimeNotEnteredCount() {
				// 获取当前时间范围内的数据
				const rangeRecords = this.getRecordsInTimeRange();
				if (!rangeRecords || !Array.isArray(rangeRecords)) {
					return 0;
				}
				return rangeRecords.filter(v => !v.enterTime || v.enterTime === null || v.enterTime === undefined).length;
			},

			getRealtimeInProgressCount() {
				// 获取当前时间范围内的数据
				const rangeRecords = this.getRecordsInTimeRange();
				if (!rangeRecords || !Array.isArray(rangeRecords)) {
					return 0;
				}
				return rangeRecords.filter(v =>
					(v.enterTime && v.enterTime !== null && v.enterTime !== undefined) &&
					(!v.leaveTime || v.leaveTime === null || v.leaveTime === undefined)
				).length;
			},

			getRealtimeLeftCount() {
				// 获取当前时间范围内的数据
				const rangeRecords = this.getRecordsInTimeRange();
				if (!rangeRecords || !Array.isArray(rangeRecords)) {
					return 0;
				}
				return rangeRecords.filter(v =>
					(v.enterTime && v.enterTime !== null && v.enterTime !== undefined) &&
					(v.leaveTime && v.leaveTime !== null && v.leaveTime !== undefined)
				).length;
			},

			// 获取当前时间范围内的记录数据
			getRecordsInTimeRange() {
				// 如果原始数据不存在，返回空数组
				if (!this.originalRealtimeRecords || !Array.isArray(this.originalRealtimeRecords)) {
					return [];
				}

				// 如果没有设置时间范围，返回所有原始数据
				if (!this.realtimeStartDate || !this.realtimeEndDate) {
					return this.originalRealtimeRecords;
				}

				// 根据设置的时间范围筛选数据
				const startTime = new Date(this.realtimeStartDate + ' 00:00:00').getTime();
				const endTime = new Date(this.realtimeEndDate + ' 23:59:59').getTime();

				return this.originalRealtimeRecords.filter(record => {
					const appointmentTime = new Date(record.appointmentTime).getTime();
					return appointmentTime >= startTime && appointmentTime <= endTime;
				});
			},

			// 违规统计区域专用的计数方法 - 始终基于原始violations计算全部统计
			getStatisticsNotEnteredCount(car) {
				// 始终基于原始violations计算，显示该车辆未进场的总违规次数
				const violations = car.originalViolations || car.violations;
				if (!violations || !Array.isArray(violations)) {
					return 0;
				}
				return violations.filter(v => !v.enterTime || v.enterTime === null || v.enterTime === undefined).length;
			},

			getStatisticsInProgressCount(car) {
				// 始终基于原始violations计算，显示该车辆未离场的总违规次数
				const violations = car.originalViolations || car.violations;
				if (!violations || !Array.isArray(violations)) {
					return 0;
				}
				return violations.filter(v =>
					(v.enterTime && v.enterTime !== null && v.enterTime !== undefined) &&
					(!v.leaveTime || v.leaveTime === null || v.leaveTime === undefined)
				).length;
			},

			getStatisticsLeftCount(car) {
				// 始终基于原始violations计算，显示该车辆已离场的总违规次数
				const violations = car.originalViolations || car.violations;
				if (!violations || !Array.isArray(violations)) {
					return 0;
				}
				return violations.filter(v =>
					(v.enterTime && v.enterTime !== null && v.enterTime !== undefined) &&
					(v.leaveTime && v.leaveTime !== null && v.leaveTime !== undefined)
				).length;
			},

			// 获取指定车辆在统计时间范围内的违规记录
			getCarViolationsInStatisticsRange(plateNumber) {
				if (!this.statisticsStartDate || !this.statisticsEndDate) {
					return [];
				}

				const startTime = new Date(this.statisticsStartDate + ' 00:00:00').getTime();
				const endTime = new Date(this.statisticsEndDate + ' 23:59:59').getTime();

				return this.originalRealtimeRecords.filter(record => {
					const appointmentTime = new Date(record.appointmentTime).getTime();
					return record.plateNumber === plateNumber &&
						appointmentTime >= startTime &&
						appointmentTime <= endTime;
				});
			},
			showRealtimeCalendar() {
				// 强制更新最大日期
				this.currentMaxDate = this.getCurrentDate();

				const endDate = this.realtimeEndDate || this.getCurrentDate();
				const startDate = this.realtimeStartDate || this.getLastWeekDate();
				console.log('showRealtimeCalendar 调用 - startDate:', startDate, 'endDate:', endDate);
				console.log('showRealtimeCalendar 调用 - currentMaxDate:', this.currentMaxDate);
				console.log('showRealtimeCalendar 调用 - defaultDate将设置为:', [startDate, endDate]);
				this.realtimeCalendarDefaultDate = [startDate, endDate];
				this.showRealtimeCalendarPicker = true;
			},
			onRealtimeCalendarConfirm(e) {
				if (e) {
					this.realtimeStartDate = e[0];
					this.realtimeEndDate = e[e.length - 1];
					this.showRealtimeCalendarPicker = false;
					// 更新实时记录数据
					this.updateRealtimeRecords();
				} else {
					uni.showToast({
						title: '请选择日期范围',
						icon: 'none'
					});
				}
			},
			updateRealtimeRecords() {
				try {
					const startTime = new Date(this.realtimeStartDate + ' 00:00:00').getTime();
					const endTime = new Date(this.realtimeEndDate + ' 23:59:59').getTime();

					// 如果没有保存原始数据，先保存（使用初始的完整数据）
					if (!this.originalRealtimeRecords.length) {
						// 重新获取完整的原始数据
						this.originalRealtimeRecords = [...this.getAllRealtimeRecords()];
					}

					// 筛选符合日期范围的记录（使用预约时间进行筛选）
					this.realtimeRecords = this.originalRealtimeRecords.filter(record => {
						const appointmentTime = new Date(record.appointmentTime).getTime();
						return appointmentTime >= startTime && appointmentTime <= endTime;
					}).sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});

					// 如果有选中的实时状态,再次筛选
					if (this.realtimeSelectedStatus) {
						this.filterRealtimeRecordsByStatus();
					}

					// 显示筛选结果提示
					uni.showToast({
						title: `已筛选出${this.realtimeRecords.length}条记录`,
						icon: 'none',
						duration: 2000
					});

				} catch (error) {
					console.error('更新实时记录数据失败:', error);
					uni.showToast({
						title: '筛选数据失败',
						icon: 'none'
					});
				}
			},
			resetRealtimeRecords() {
				this.realtimeStartDate = '';
				this.realtimeEndDate = '';
				this.realtimeSelectedStatus = ''; // 清除实时状态选择
				if (this.originalRealtimeRecords.length) {
					this.realtimeRecords = [...this.originalRealtimeRecords].sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});
				}
			},

			// 获取完整的原始实时记录数据
			getAllRealtimeRecords() {
				// 返回已加载的原始数据，如果没有则返回空数组
				return this.originalRealtimeRecords.length > 0 ? this.originalRealtimeRecords : [];
			},
			// 初始化默认日期范围
			initDefaultDateRange() {
				const today = new Date();
				const lastWeek = new Date();
				lastWeek.setDate(today.getDate() - 7); // 修改为7天前

				this.realtimeStartDate = this.formatDate(lastWeek);
				this.realtimeEndDate = this.formatDate(today);

				// 初始化时更新记录
				this.updateRealtimeRecords();
			},

			// 选择实时记录状态
			selectStatus(status) {
				this.selectedStatus = this.selectedStatus === status ? '' : status;
				this.filterRecordsByStatus();
			},

			// 选择实时违规记录状态（独立）
			selectRealtimeStatus(status) {
				this.realtimeSelectedStatus = this.realtimeSelectedStatus === status ? '' : status;
				this.filterRealtimeRecordsByStatus();
			},

			// 选择违规统计状态（全局，已废弃）
			selectStatisticsStatus(status) {
				this.statisticsSelectedStatus = this.statisticsSelectedStatus === status ? '' : status;
				this.filterStatisticsByStatus();
			},

			// 选择单个车辆的统计状态
			selectCarStatisticsStatus(car, status) {
				// 切换该车辆的选中状态
				const newStatus = car.selectedStatus === status ? '' : status;

				// 更新该车辆的选中状态
				car.selectedStatus = newStatus;

				// 筛选该车辆的违规记录
				this.filterCarViolationsByStatus(car);
			},

			// 筛选单个车辆的违规记录
			filterCarViolationsByStatus(car) {
				if (!car.selectedStatus) {
					// 如果取消选择,恢复原始违规数据
					car.violations = [...car.originalViolations];
					car.filteredCount = car.originalViolations.length;
					return;
				}

				// 基于原始violations进行筛选
				const filteredViolations = car.originalViolations.filter(violation => {
					switch (car.selectedStatus) {
						case 'not-entered':
							return !violation.enterTime;
						case 'in-progress':
							return violation.enterTime && !violation.leaveTime;
						case 'has-left':
							return violation.enterTime && violation.leaveTime;
						default:
							return true;
					}
				});

				// 更新该车辆的违规记录
				car.violations = filteredViolations;
				car.filteredCount = filteredViolations.length;

				// 显示筛选结果提示
				const statusText = this.getStatusText(car.selectedStatus);
				uni.showToast({
					title: `${car.plateNumber}: 已筛选出${car.filteredCount}条${statusText}违规记录`,
					icon: 'none',
					duration: 2000
				});
			},

			// 根据状态筛选实时记录
			filterRecordsByStatus() {
				if (!this.selectedStatus) {
					// 如果取消选择,恢复原始数据
					this.realtimeRecords = [...this.originalRealtimeRecords].sort((a, b) => {
						// 按预约时间降序排序（预约时间越晚越靠前）
						const timeA = new Date(a.appointmentTime).getTime();
						const timeB = new Date(b.appointmentTime).getTime();
						return timeB - timeA;
					});
					return;
				}

				// 直接更新 realtimeRecords
				this.realtimeRecords = this.originalRealtimeRecords.filter(record => {
					switch (this.selectedStatus) {
						case 'not-entered':
							return !record.enterTime;
						case 'in-progress':
							return record.enterTime && !record.leaveTime;
						case 'has-left':
							return record.enterTime && record.leaveTime;
						default:
							return true;
					}
				}).sort((a, b) => {
					// 按预约时间降序排序（预约时间越晚越靠前）
					const timeA = new Date(a.appointmentTime).getTime();
					const timeB = new Date(b.appointmentTime).getTime();
					return timeB - timeA;
				});

				// 显示筛选结果提示
				uni.showToast({
					title: `已筛选出${this.realtimeRecords.length}条记录`,
					icon: 'none',
					duration: 2000
				});
			},

			// 根据实时状态筛选实时记录（独立筛选）
			filterRealtimeRecordsByStatus() {
				if (!this.realtimeSelectedStatus) {
					// 如果取消选择,恢复到当前时间范围的数据
					this.updateRealtimeRecords();
					return;
				}

				// 基于当前时间范围内的数据进行状态筛选
				let baseRecords = [];
				if (this.realtimeStartDate && this.realtimeEndDate) {
					const startTime = new Date(this.realtimeStartDate + ' 00:00:00').getTime();
					const endTime = new Date(this.realtimeEndDate + ' 23:59:59').getTime();
					baseRecords = this.originalRealtimeRecords.filter(record => {
						const appointmentTime = new Date(record.appointmentTime).getTime();
						return appointmentTime >= startTime && appointmentTime <= endTime;
					});
				} else {
					baseRecords = [...this.originalRealtimeRecords];
				}

				// 根据状态进一步筛选
				this.realtimeRecords = baseRecords.filter(record => {
					switch (this.realtimeSelectedStatus) {
						case 'not-entered':
							// 未进场：没有进场时间或进场时间为null/undefined
							return !record.enterTime || record.enterTime === null || record.enterTime === undefined;
						case 'in-progress':
							// 在场：有进场时间但没有离场时间
							return (record.enterTime && record.enterTime !== null && record.enterTime !== undefined) &&
							       (!record.leaveTime || record.leaveTime === null || record.leaveTime === undefined);
						case 'has-left':
							// 已离场：既有进场时间又有离场时间
							return (record.enterTime && record.enterTime !== null && record.enterTime !== undefined) &&
							       (record.leaveTime && record.leaveTime !== null && record.leaveTime !== undefined);
						default:
							return true;
					}
				}).sort((a, b) => {
					// 按预约时间降序排序（预约时间越晚越靠前）
					const timeA = new Date(a.appointmentTime).getTime();
					const timeB = new Date(b.appointmentTime).getTime();
					return timeB - timeA;
				});

				// 显示筛选结果提示
				uni.showToast({
					title: `已筛选出${this.realtimeRecords.length}条记录`,
					icon: 'none',
					duration: 2000
				});
			},

			// 根据状态筛选违规统计数据
			filterStatisticsByStatus() {
				if (!this.statisticsSelectedStatus) {
					// 如果取消选择,恢复原始违规数据
					this.highRiskCars = this.highRiskCars.map(car => ({
						...car,
						violations: [...car.originalViolations], // 恢复原始violations
						count: car.originalViolations.length, // 显示全部违规次数
						filteredCount: car.originalViolations.length // 筛选后的数量等于全部数量
					}));
					return;
				}

				// 基于原始violations进行筛选，不修改原始数据
				this.highRiskCars = this.highRiskCars.map(car => {
					const filteredViolations = car.originalViolations.filter(violation => {
						switch (this.statisticsSelectedStatus) {
							case 'not-entered':
								return !violation.enterTime;
							case 'in-progress':
								return violation.enterTime && !violation.leaveTime;
							case 'has-left':
								return violation.enterTime && violation.leaveTime;
							default:
								return true;
						}
					});

					return {
						...car,
						violations: filteredViolations, // 筛选后的违规记录用于显示
						count: car.originalViolations.length, // 车牌旁边显示总违规次数
						filteredCount: filteredViolations.length // 筛选后的数量
					};
				}); // 保留所有车辆，即使某个状态下没有违规记录

				// 显示筛选结果提示
				const totalFilteredViolations = this.highRiskCars.reduce((sum, car) => sum + car.filteredCount, 0);
				const statusText = this.getStatusText(this.statisticsSelectedStatus);
				uni.showToast({
					title: `已筛选出${totalFilteredViolations}条${statusText}违规记录`,
					icon: 'none',
					duration: 2000
				});
			},

			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case 'not-entered':
						return '未进场';
					case 'in-progress':
						return '未离场';
					case 'has-left':
						return '已离场';
					default:
						return '';
				}
			},
			// 违规统计相关方法
			showStatisticsCalendar() {
				// 强制更新最大日期
				this.currentMaxDate = this.getCurrentDate();

				const endDate = this.statisticsEndDate || this.getCurrentDate();
				const startDate = this.statisticsStartDate || this.getLastWeekDate();
				console.log('showStatisticsCalendar 调用 - currentMaxDate:', this.currentMaxDate);
				this.statisticsCalendarDefaultDate = [startDate, endDate];
				this.showStatisticsCalendarPicker = true;
			},

			onStatisticsCalendarConfirm(e) {
				if (e) {
					this.statisticsStartDate = e[0];
					this.statisticsEndDate = e[e.length - 1];
					this.showStatisticsCalendarPicker = false;
					// 更新违规统计数据
					this.updateStatisticsData();
				}
			},

			updateStatisticsData() {
				try {
					console.log('📊 开始更新统计数据');
					console.log('📊 实时记录数量:', this.realtimeRecords.length);
					console.log('📊 原始记录数量:', this.originalRealtimeRecords.length);

					const startTime = new Date(this.statisticsStartDate + ' 00:00:00').getTime();
					const endTime = new Date(this.statisticsEndDate + ' 23:59:59').getTime();

					// 创建一个Map来统计时间范围内每辆车的违规次数和信息
					const violationStats = new Map();

					// 如果原始记录为空，使用当前记录
					const recordsToProcess = this.originalRealtimeRecords.length > 0 ?
						this.originalRealtimeRecords : this.realtimeRecords;

					console.log('📊 处理记录数量:', recordsToProcess.length);

					// 遍历实时违规记录，只统计在选定日期范围内的记录
					recordsToProcess.forEach(record => {
						// 使用预约时间进行筛选
						const appointmentTime = new Date(record.appointmentTime).getTime();

						// 检查预约时间是否在选定范围内
						if (appointmentTime >= startTime && appointmentTime <= endTime) {
							const plateNumber = record.plateNumber;

							if (!violationStats.has(plateNumber)) {
								// 初始化该车辆的统计信息
								violationStats.set(plateNumber, {
									plateNumber,
									count: 0,
									ownerName: record.ownerName || '',
									phone: record.phone || '',
									address: record.address || '',
									isNewEnergy: this.isNewEnergyPlate(plateNumber),
									isExpanded: false,
									violations: []
								});
							}

							const carStats = violationStats.get(plateNumber);
							carStats.count++;

							// 更新车主信息
							if (record.ownerName) carStats.ownerName = record.ownerName;
							if (record.phone) carStats.phone = record.phone;
							if (record.address) carStats.address = record.address;

							// 添加违规记录
							carStats.violations.push({
								time: record.time,
								violationType: record.violationType,
								status: record.status,
								appointmentReason: record.appointmentReason,
								enterTime: record.enterTime,
								leaveTime: record.leaveTime,
								appointmentTime: record.appointmentTime
							});
						}
					});

					// 筛选出违规次数超过3次的车辆并按违规次数降序排序
					this.highRiskCars = Array.from(violationStats.values())
						.filter(car => car.count >= 3)
						.sort((a, b) => b.count - a.count)
						.map(car => ({
							...car,
							originalViolations: [...car.violations], // 保存原始的violations数据
							filteredCount: car.violations.length, // 初始时筛选后的数量等于全部数量
							selectedStatus: '' // 初始时没有选中任何状态
						}));

					// 保存原始高风险车辆数据
					if (!this.originalHighRiskCars.length) {
						this.originalHighRiskCars = [...this.highRiskCars];
					}

					// 显示筛选结果提示
					uni.showToast({
						title: `已筛选出${this.highRiskCars.length}辆高风险车辆`,
						icon: 'none',
						duration: 2000
					});

				} catch (error) {
					console.error('更新违规统计数据失败:', error);
					uni.showToast({
						title: '更新统计数据失败',
						icon: 'none'
					});
				}
			},
			processRealtimeRecords() {
				try {
					// 处理实时记录的逻辑
					const startTime = new Date(this.realtimeStartDate).getTime();
					const endTime = new Date(this.realtimeEndDate).getTime();

					this.filteredRealtimeRecords = this.realtimeRecords.filter(record => {
						const recordTime = new Date(record.time).getTime();
						return recordTime >= startTime && recordTime <= endTime;
					});
				} catch (error) {
					console.error('更新实时记录失败:', error);
					uni.showToast({
						title: '更新实时记录失败',
						icon: 'none'
					});
				}
			},

			processStatistics() {
				try {
					const startTime = new Date(this.statisticsStartDate).getTime();
					const endTime = new Date(this.statisticsEndDate).getTime();

					// 处理统计数据的逻辑
					this.filteredStatisticsRecords = this.originalStatisticsRecords.filter(record => {
						const recordTime = new Date(record.time).getTime();
						return recordTime >= startTime && recordTime <= endTime;
					});

					// 更新高风险车辆数量
					this.highRiskCount = this.filteredStatisticsRecords.filter(record => {
						return record.riskLevel === 'high';
					}).length;
				} catch (error) {
					console.error('更新违规统计数据失败:', error);
					uni.showToast({
						title: '更新统计数据失败',
						icon: 'none'
					});
				}
			},

			// 处理滑动操作
			handleSwipeAction(e) {
				const listIndex = parseInt(e.name); // 列表项索引
				const buttonIndex = e.index; // 按钮索引 (0=通过, 1=拒绝)
				const record = this.realtimeRecords[listIndex];

				if (buttonIndex === 0) {
					// 通过操作
					this.approveViolation(record, listIndex);
				} else if (buttonIndex === 1) {
					// 拒绝操作
					this.rejectViolation(record, listIndex);
				}
			},

			// 通过违规处理
			approveViolation(record, index) {
				uni.showModal({
					title: '确认通过',
					content: `确认通过车牌 ${record.plateNumber} 的违规处理申请？`,
					success: (res) => {
						if (res.confirm) {
							// 更新记录状态
							this.$set(this.realtimeRecords[index], 'statusText', '已处理');

							// 关闭滑动面板
							if (this.$refs.uSwipeAction && typeof this.$refs.uSwipeAction.closeOther ===
								'function') {
								this.$refs.uSwipeAction.closeOther(index);
							}

							uni.showToast({
								title: '已通过处理',
								icon: 'success'
							});
						} else {
							// 取消时关闭滑动状态
							if (this.$refs.uSwipeAction && typeof this.$refs.uSwipeAction.closeOther ===
								'function') {
								this.$refs.uSwipeAction.closeOther(index);
							}
						}
					}
				});
			},

			// 拒绝违规处理
			rejectViolation(record, index) {
				uni.showModal({
					title: '确认拒绝',
					content: `确认拒绝车牌 ${record.plateNumber} 的违规处理申请？`,
					success: (res) => {
						if (res.confirm) {
							// 更新记录状态
							this.$set(this.realtimeRecords[index], 'statusText', '已拒绝');

							// 关闭滑动面板
							if (this.$refs.uSwipeAction && typeof this.$refs.uSwipeAction.closeOther ===
								'function') {
								this.$refs.uSwipeAction.closeOther(index);
							}

							uni.showToast({
								title: '已拒绝处理',
								icon: 'none'
							});
						} else {
							// 取消时关闭滑动状态
							if (this.$refs.uSwipeAction && typeof this.$refs.uSwipeAction.closeOther ===
								'function') {
								this.$refs.uSwipeAction.closeOther(index);
							}
						}
					}
				});
			},

			// 从数据库加载数据
			async loadDataFromDatabase() {
				try {
					uni.showLoading({ title: '加载中...' });

					// 并行加载各种数据
					await Promise.all([
						this.loadViolationStatistics(),
						this.loadRealtimeViolations(),
						this.loadUserCreditScore()
					]);

					// 数据加载完成后更新界面
					this.updateStatisticsData();
					this.updateRealtimeRecords();

				} catch (error) {
					console.error('加载数据失败:', error);
					apiUtils.handleApiError(error, '数据加载失败');
					// 如果数据库加载失败，使用静态数据作为备用
					this.initializeRandomTimeData();
				} finally {
					uni.hideLoading();
				}
			},

			// 加载违规统计数据
			async loadViolationStatistics() {
				try {
					const params = {
						...apiUtils.formatDateRange(this.statisticsStartDate, this.statisticsEndDate),
						type: 'statistics'
					};

					const data = await violationApi.getStatistics(params);

					// 更新统计数据
					this.monthViolations = data.monthViolations || 0;
					this.totalViolations = data.totalViolations || 0;
					this.lastViolationDate = data.lastViolationDate || '';

					// 更新图表数据
					if (data.chartData) {
						this.chartData = data.chartData;
					}

				} catch (error) {
					console.error('加载违规统计失败:', error);
					// 使用默认值
					this.monthViolations = 0;
					this.totalViolations = 0;
					this.lastViolationDate = '';
				}
			},

			// 加载实时违规记录
			async loadRealtimeViolations() {
				try {
					const params = {
						...apiUtils.formatDateRange(this.realtimeStartDate, this.realtimeEndDate),
						...apiUtils.formatPagination(1, 100),
						status: this.realtimeSelectedStatus || undefined
					};

					const data = await violationApi.getViolations(params);

					// 转换数据格式
					this.realtimeRecords = data.list.map(item => ({
						id: item.id,
						plateNumber: item.plateNumber,
						appointmentReason: item.appointmentReason || '回家',
						violationType: item.violationType,
						isNewEnergy: this.isNewEnergyPlate(item.plateNumber),
						ownerName: item.ownerName,
						phone: item.phone,
						address: item.address,
						appointmentTime: item.appointmentTime,
						enterTime: item.enterTime,
						leaveTime: item.leaveTime,
						isExpanded: false
					}));

					// 保存原始数据
					this.originalRealtimeRecords = [...this.realtimeRecords];

				} catch (error) {
					console.error('加载实时违规记录失败:', error);
					// 使用生成的模拟数据作为备用
					this.realtimeRecords = this.generateRealtimeRecords();
					this.originalRealtimeRecords = [...this.realtimeRecords];
					console.log('📋 使用模拟数据，共', this.realtimeRecords.length, '条违规记录');
				}
			},



			// 加载用户信用分
			async loadUserCreditScore() {
				try {
					// 获取当前用户ID
					const userInfo = uni.getStorageSync('userInfo');
					if (!userInfo || !userInfo.id) {
						console.warn('未找到用户信息');
						return;
					}

					// 这里可以调用获取用户信用分的API
					// const creditData = await ownerApi.getCreditScore(userInfo.id);
					// this.creditScore = creditData.score || 85;

					// 暂时使用默认值
					this.creditScore = 85;

				} catch (error) {
					console.error('加载用户信用分失败:', error);
					this.creditScore = 85;
				}
			}
		},
		onShow() {
			// 页面显示时通知TabBar检查当前页面
			this.$nextTick(() => {
				console.log('📱 [管家违规页面] 页面显示');
			});
		},
		async mounted() {
			// 获取用户角色
			this.getUserRole();

			// 初始化当前最大日期
			this.currentMaxDate = this.getCurrentDate();
			console.log('mounted - 初始化 currentMaxDate:', this.currentMaxDate);

			// 加载搜索历史
			this.loadSearchHistory();

			// 初始化违规统计和实时记录的时间范围
			const today = new Date();
			const lastWeek = new Date();
			lastWeek.setDate(today.getDate() - 7);

			// 违规统计的时间范围（独立）
			this.statisticsStartDate = this.formatDate(lastWeek);
			this.statisticsEndDate = this.formatDate(today);

			// 实时记录的时间范围（独立）
			this.realtimeStartDate = this.formatDate(lastWeek);
			this.realtimeEndDate = this.formatDate(today);

			// 从数据库加载数据
			await this.loadDataFromDatabase();
		}
}
</script>

<style lang="scss">
	.violation-container {
		padding: 12rpx 6rpx; // 左右间距
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom)); // 底部间距避免被导航栏遮挡，适配安全区域
		background: #f5f6fa;
		min-height: 100vh;

		.credit-score-section {
			background: #fff;
			border-radius: 12rpx;
			padding: 20rpx;
			margin-bottom: 16rpx;
			text-align: center;

			.credit-ring {
				margin-bottom: 16rpx;
			}

			.credit-inner {
				display: flex;
				flex-direction: column;
				align-items: center;

				.credit-num {
					font-size: 36rpx;
					font-weight: bold;
					color: #2979ff;
				}

				.credit-label {
					font-size: 20rpx;
					color: #666;
				}
			}

			.credit-message {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				font-size: 24rpx;

				&.warning {
					color: #ff9900;
				}
			}
		}

		.violation-stats {
			display: flex;
			justify-content: space-between;
			gap: 16rpx;
			margin: 16rpx;

			.stats-card {
				flex: 1;
				padding: 16rpx;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				color: #fff;

				&:nth-child(1) {
					background: linear-gradient(135deg, #ff4d4f, #ff7875);
				}

				&:nth-child(2) {
					background: linear-gradient(135deg, #1890ff, #40a9ff);
				}

				&:nth-child(3) {
					background: linear-gradient(135deg, #722ed1, #b37feb);
				}

				.stats-title {
					font-size: 24rpx;
					opacity: 0.9;
					margin-bottom: 8rpx;
				}

				.stats-num {
					font-size: 36rpx;
					font-weight: bold;
				}

				.stats-date {
					font-size: 28rpx;
					font-weight: 500;
				}
			}
		}

		.chart-section {
			background: #fff;
			border-radius: 12rpx;
			padding: 16rpx;
			margin-bottom: 16rpx;

			.chart-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;

				.chart-title {
					font-size: 26rpx;
					font-weight: bold;
				}
			}
		}

		.violation-list {
			background: #fff;
			border-radius: 12rpx;
			padding: 16rpx;

			.list-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;
				font-size: 26rpx;
				font-weight: bold;
			}

			// 紧凑的违规记录项
			.violation-item-compact {
				margin-bottom: 16rpx;
				background: #fff;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
				overflow: hidden;
				transition: all 0.3s ease;

				&.expanded {
					box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
				}

				// 紧凑的一行显示
				.compact-row {
					display: flex;
					align-items: center;
					padding: 24rpx 32rpx;
					cursor: pointer;
					transition: background-color 0.2s ease;

					&:active {
						background-color: #f8f9fa;
					}

					.main-info {
						flex: 1;
						display: flex;
						align-items: center;
						gap: 12rpx;
						overflow: hidden;

						// 车牌号
						.plate-number-compact {
							display: inline-block;
							font-size: 28rpx;
							font-weight: bold;
							padding: 8rpx 16rpx;
							border-radius: 8rpx;
							font-family: "微软雅黑";
							letter-spacing: 1rpx;
							min-width: 140rpx;
							text-align: center;
							flex-shrink: 0;

							&.blue-plate {
								background: linear-gradient(180deg, #0C4FC5 0%, #216FEF 100%);
								color: #FFFFFF;
								border: 1px solid #0C4FC5;
							}

							&.green-plate {
								background: linear-gradient(180deg, #6AD390 0%, #D0F1E4 100%);
								color: #000000;
								border: 1px solid #6AD390;
							}
						}

						// 状态标签
						.status-tag {
							padding: 8rpx 16rpx;
							border-radius: 16rpx;
							font-size: 24rpx;
							flex-shrink: 0;

							.status-text {
								color: #fff;
								font-weight: 500;
							}

							&.status-not-processed {
								background: #ff4d4f;
							}

							&.status-processed {
								background: #52c41a;
							}

							&.status-processing {
								background: #ff9500; // 修改为橙色，更醒目
							}

							&.status-default {
								background: #8c8c8c;
							}
						}

						// 违规原因简化显示
						.reason-short {
							font-size: 24rpx;
							color: #ff4d4f;
							flex-shrink: 0;
							max-width: 140rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%);
							padding: 6rpx 12rpx;
							border-radius: 12rpx;
							border: 1px solid #ffccc7;
							font-weight: 500;
							text-align: center;
						}

						// 时间显示
						.time-display {
							font-size: 22rpx;
							color: #999;
							margin-left: auto;
							flex-shrink: 0;
							min-width: 100rpx;
							text-align: right;
						}
					}

					// 展开图标
					.expand-icon {
						margin-left: 16rpx;
						flex-shrink: 0;
						transition: transform 0.3s ease;
					}
				}

				// 展开后的详细内容
				.detail-content {
					padding: 0 32rpx 32rpx;
					border-top: 1px solid #f0f0f0;
					background: #fafafa;

					.detail-section {
						padding-top: 24rpx;

						// 时间信息卡片
						.time-cards {
							display: flex;
							flex-direction: column;
							gap: 16rpx;
							margin-bottom: 24rpx;

							.time-card {
								display: flex;
								align-items: center;
								padding: 16rpx 20rpx;
								background: #fff;
								border-radius: 12rpx;
								box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);

								.card-icon {
									margin-right: 16rpx;
									width: 40rpx;
									height: 40rpx;
									display: flex;
									align-items: center;
									justify-content: center;
									border-radius: 8rpx;

									.emoji-icon {
										font-size: 24rpx;
									}
								}

								.card-content {
									flex: 1;

									.card-label {
										font-size: 22rpx;
										color: #666;
										display: block;
										margin-bottom: 4rpx;
									}

									.card-time {
										font-size: 26rpx;
										color: #333;
										font-weight: 500;
									}
								}

								&.appointment-card .card-icon {
									background: rgba(41, 121, 255, 0.1);
								}

								&.enter-card .card-icon {
									background: rgba(82, 196, 26, 0.1);
								}

								&.leave-card .card-icon {
									background: rgba(255, 77, 79, 0.1);
								}
							}
						}

						// 违规详情
						.violation-detail {
							.detail-row {
								display: flex;
								align-items: center;
								margin-bottom: 16rpx;
								padding: 16rpx 20rpx;
								background: #fff;
								border-radius: 12rpx;
								box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);

								.detail-tag {
									display: flex;
									align-items: center;
									margin-right: 16rpx;
									min-width: 120rpx;

									.tag-label {
										font-size: 22rpx;
										color: #666;
										margin-left: 8rpx;
									}
								}

								.detail-value {
									flex: 1;
									font-size: 26rpx;
									color: #333;

									&.status-not-processed {
										color: #ff4d4f;
									}

									&.status-processed {
										color: #52c41a;
									}

									&.status-processing {
										color: #ff9500; // 修改为橙色，与标签背景色一致
									}
								}
							}
						}
					}

					// 紧凑型三行布局样式
					.compact-detail-layout {
						padding-top: 24rpx;

						// 第一行：时间流程
						.time-flow-row {
							display: flex;
							align-items: stretch;
							justify-content: space-between;
							margin-bottom: 16rpx;
							padding: 16rpx;
							background: #f8f9fa;
							border-radius: 12rpx;

							.time-item {
								display: flex;
								flex-direction: column;
								align-items: flex-start;
								flex: 1;
								min-width: 0;
								max-width: 33.33%;
								position: relative;
								padding: 16rpx 12rpx;
								background: #fff;
								border-radius: 12rpx;
								margin-right: 12rpx;
								box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
								border: 1px solid #f0f0f0;

								&:last-child {
									margin-right: 0;
								}

								// 时间头部（图标+标签）
								.time-header {
									display: flex;
									align-items: center;
									margin-bottom: 12rpx;

									.time-icon {
										font-size: 20rpx;
										margin-right: 8rpx;
									}

									.time-label {
										font-size: 22rpx;
										color: #999;
										white-space: nowrap;
										font-weight: 400;
									}
								}

								// 时间显示区域
								.time-display {
									display: flex;
									flex-direction: column;
									align-items: flex-start;
									width: 100%;

									.time-main {
										font-size: 32rpx;
										color: #333;
										font-weight: 600;
										line-height: 1.2;
										margin-bottom: 4rpx;
									}

									.time-date {
										font-size: 20rpx;
										color: #999;
										line-height: 1;
									}
								}

								// 不同状态的样式
								&:nth-child(1) {
									border-left: 4rpx solid #1890ff; // 预约 - 蓝色
								}

								&:nth-child(2) {
									border-left: 4rpx solid #52c41a; // 进场 - 绿色
								}

								&:nth-child(3) {
									border-left: 4rpx solid #ff9500; // 离场 - 橙色
								}
							}
						}

						// 第二行：停车时长
						.duration-row {
							display: flex;
							align-items: center;
							margin-bottom: 16rpx;
							padding: 20rpx 24rpx;
							background: linear-gradient(135deg, #fff9f0 0%, #ffffff 100%);
							border-radius: 16rpx;
							box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.08);
							border: 1px solid #fff2e8;
							position: relative;
							overflow: hidden;

							// 添加左侧装饰条
							&::before {
								content: '';
								position: absolute;
								left: 0;
								top: 0;
								bottom: 0;
								width: 4rpx;
								background: linear-gradient(to bottom, #ff9500, #ffad33);
							}

							.duration-icon {
								font-size: 26rpx;
								margin-right: 12rpx;
								padding: 10rpx;
								border-radius: 50%;
								background: rgba(255, 149, 0, 0.15);
								display: flex;
								align-items: center;
								justify-content: center;
								min-width: 46rpx;
								height: 46rpx;
							}

							.duration-label {
								font-size: 26rpx;
								color: #666;
								margin-right: 12rpx;
								font-weight: 500;
							}

							.duration-value {
								font-size: 32rpx;
								font-weight: 600;
								padding: 6rpx 16rpx;
								border-radius: 20rpx;
								position: relative;

								&.duration-short {
									color: #52c41a;
									background: rgba(82, 196, 26, 0.1);
									border: 1px solid rgba(82, 196, 26, 0.2);
								}

								&.duration-medium {
									color: #ff9500;
									background: rgba(255, 149, 0, 0.1);
									border: 1px solid rgba(255, 149, 0, 0.2);
								}

								&.duration-long {
									color: #ff4d4f;
									background: rgba(255, 77, 79, 0.1);
									border: 1px solid rgba(255, 77, 79, 0.2);
									animation: pulse 2s infinite;
								}
							}
						}

						// 第三行：状态信息
						.status-row {
							display: flex;
							align-items: center;
							padding: 20rpx 24rpx;
							background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
							border-radius: 16rpx;
							box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.06);
							border: 1px solid #f0f9e8;
							position: relative;

							.status-item {
								display: flex;
								align-items: center;
								padding: 8rpx 16rpx;
								border-radius: 20rpx;
								transition: all 0.3s ease;

								.status-icon {
									font-size: 24rpx;
									margin-right: 8rpx;
								}

								.status-text {
									font-size: 28rpx;
									color: #333;
									font-weight: 500;

									&.status-not-processed {
										color: #ff4d4f;
									}

									&.status-processed {
										color: #52c41a;
									}

									&.status-processing {
										color: #ff9500;
									}
								}

								// 违规原因样式
								&:first-child {
									background: rgba(255, 77, 79, 0.08);
									border: 1px solid rgba(255, 77, 79, 0.15);

									.status-text {
										color: #ff4d4f;
										font-weight: 600;
									}
								}

								// 车辆状态样式
								&:last-child {
									background: rgba(82, 196, 26, 0.08);
									border: 1px solid rgba(82, 196, 26, 0.15);

									.status-text.status-processed {
										color: #52c41a;
										font-weight: 600;
									}

									.status-text.status-processing {
										color: #ff9500;
										background: rgba(255, 149, 0, 0.08);
										border: 1px solid rgba(255, 149, 0, 0.15);
										padding: 4rpx 12rpx;
										border-radius: 12rpx;
									}

									.status-text.status-not-processed {
										color: #ff4d4f;
										background: rgba(255, 77, 79, 0.08);
										border: 1px solid rgba(255, 77, 79, 0.15);
										padding: 4rpx 12rpx;
										border-radius: 12rpx;
									}
								}
							}

							.status-divider {
								margin: 0 20rpx;
								color: #d9d9d9;
								font-size: 28rpx;
								font-weight: 300;
							}
						}
					}

					// 添加动画效果
					@keyframes pulse {
						0% {
							transform: scale(1);
							box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
						}

						50% {
							transform: scale(1.02);
							box-shadow: 0 0 0 8rpx rgba(255, 77, 79, 0.1);
						}

						100% {
							transform: scale(1);
							box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
						}
					}

					@keyframes slideIn {
						from {
							opacity: 0;
							transform: translateY(20rpx);
						}

						to {
							opacity: 1;
							transform: translateY(0);
						}
					}

					// 为展开内容添加滑入动画
					.compact-detail-layout {
						animation: slideIn 0.3s ease-out;
					}
				}
			}
		}

		// 管家界面样式
		.housekeeper-view {

			// 智能搜索容器样式
			.smart-search-container {
				display: flex;
				gap: 12rpx;
				padding: 12rpx;
				background: #fff;
				border-radius: 8rpx;
				margin-bottom: 12rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

				.search-wrapper {
					flex: 1;
					position: relative;

					.search-input-box {
						position: relative;
						background: #f5f7fa;
						border-radius: 20rpx;
						padding: 0 16rpx;
						transition: all 0.3s ease;
						border: 2rpx solid transparent;
						display: flex;
						align-items: center;

						&.focused {
							background: #fff;
							border-color: #2979ff;
							box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.1);
						}

						&.hasText {
							.search-input {
								color: #333;
							}

							padding-right: 50rpx; // 为清空按钮留出空间
						}

						.search-input {
							flex: 1;
							height: 48rpx;
							font-size: 24rpx;
							color: #666;
							background: transparent;
							border: none;
							outline: none;
							padding: 0;

							&::placeholder {
								color: #999;
								font-size: 22rpx;
							}
						}

						.clear-btn {
							position: absolute;
							right: 12rpx;
							top: 50%;
							transform: translateY(-50%);
							width: 32rpx;
							height: 32rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							z-index: 10;

							&:active {
								opacity: 0.6;
							}
						}
					}

					// 搜索建议下拉框
					.search-suggestions {
						position: absolute;
						top: 100%;
						left: 0;
						right: 0;
						background: #fff;
						border-radius: 8rpx;
						box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
						z-index: 1000;
						margin-top: 6rpx;
						border: 1rpx solid #e8e8e8;
						overflow: hidden;

						.suggestions-scroll {
							max-height: 320rpx;
						}

						.suggestion-item {
							display: flex;
							align-items: center;
							padding: 12rpx 16rpx;
							border-bottom: 1rpx solid #f0f0f0;
							cursor: pointer;
							transition: all 0.3s ease;

							&:hover {
								background: #f8f9ff;
							}

							&:last-child {
								border-bottom: none;
							}

							.suggestion-icon {
								width: 32rpx;
								height: 32rpx;
								border-radius: 6rpx;
								background: #f5f7fa;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-right: 12rpx;

								.icon-emoji {
									font-size: 20rpx;
								}
							}

							.suggestion-content {
								flex: 1;

								.suggestion-text {
									font-size: 24rpx;
									color: #333;
									margin-bottom: 2rpx;
									display: block;
								}

								.suggestion-type {
									font-size: 20rpx;
									color: #999;
								}
							}

							.suggestion-arrow {
								color: #ccc;

								.icon-emoji {
									font-size: 16rpx;
								}
							}
						}

						.suggestions-footer {
							padding: 8rpx 16rpx;
							background: #f8f9fa;
							border-top: 1rpx solid #e8e8e8;

							.footer-text {
								font-size: 20rpx;
								color: #666;
							}
						}
					}
				}

				// 右侧操作按钮组
				.action-buttons {
					display: flex;
					gap: 8rpx;

					.action-btn {
						width: 80rpx;
						height: 56rpx;
						border-radius: 12rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						transition: all 0.3s ease;
						border: 1rpx solid #e8e8e8;

						&.active {
							background: #fff3e0;
							border-color: #f5a623;
						}

						&:hover {
							transform: translateY(-2rpx);
							box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
						}

						.btn-content {
							display: flex;
							flex-direction: column;
							align-items: center;
							gap: 2rpx;

							.icon-emoji {
								font-size: 20rpx;
								line-height: 1;
							}

							.btn-label {
								font-size: 18rpx;
								line-height: 1;
							}
						}
					}
				}
			}

			// 搜索历史面板
			.search-history-panel {
				background: #fff;
				border-radius: 8rpx;
				margin-bottom: 12rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
				overflow: hidden;

				.history-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 12rpx 16rpx;
					background: #f8f9fa;
					border-bottom: 1rpx solid #e8e8e8;

					.header-title {
						display: flex;
						align-items: center;

						.title-text {
							font-size: 24rpx;
							font-weight: 600;
							color: #333;
						}
					}

					.clear-all {
						font-size: 20rpx;
						color: #999;
						cursor: pointer;

						&:hover {
							color: #ff4d4f;
						}
					}
				}

				.history-content {
					max-height: 240rpx;
					overflow-y: auto;
				}

				.history-item {
					display: flex;
					align-items: center;
					padding: 12rpx 16rpx;
					border-bottom: 1rpx solid #f0f0f0;
					cursor: pointer;
					transition: all 0.3s ease;

					&:hover {
						background: #f8f9ff;
					}

					&:last-child {
						border-bottom: none;
					}

					.history-icon {
						width: 28rpx;
						height: 28rpx;
						border-radius: 4rpx;
						background: #f5f7fa;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 8rpx;

						.icon-emoji {
							font-size: 16rpx;
						}
					}

					.history-text {
						flex: 1;
						font-size: 22rpx;
						color: #333;
					}

					.history-time {
						font-size: 18rpx;
						color: #999;
						margin-right: 8rpx;
					}

					.history-delete {
						width: 24rpx;
						height: 24rpx;
						border-radius: 50%;
						background: #f0f0f0;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;

						&:hover {
							background: #ff4d4f;

							.icon-emoji {
								color: #fff !important;
							}
						}

						.icon-emoji {
							font-size: 14rpx;
						}
					}
				}

				.history-empty {
					padding: 40rpx 16rpx;
					text-align: center;

					.empty-text {
						font-size: 20rpx;
						color: #999;
						margin-top: 8rpx;
						display: block;
					}
				}
			}

			// 热门搜索标签
			.hot-search-tags {
				background: #fff;
				border-radius: 8rpx;
				padding: 12rpx;
				margin-bottom: 12rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

				.tags-header {
					display: flex;
					align-items: center;
					margin-bottom: 12rpx;

					.tags-title {
						font-size: 24rpx;
						font-weight: 600;
						color: #333;
					}
				}

				.tags-container {
					display: flex;
					flex-wrap: wrap;
					gap: 8rpx;

					.hot-tag {
						display: flex;
						align-items: center;
						gap: 6rpx;
						padding: 6rpx 12rpx;
						background: #f5f7fa;
						border-radius: 16rpx;
						border: 1rpx solid #e8e8e8;
						cursor: pointer;
						transition: all 0.3s ease;

						&:hover {
							background: #e6f7ff;
							border-color: #1890ff;

							.tag-text {
								color: #1890ff;
							}
						}

						.tag-text {
							font-size: 20rpx;
							color: #666;
						}

						.tag-count {
							font-size: 16rpx;
							color: #999;
							background: #e8e8e8;
							padding: 1rpx 6rpx;
							border-radius: 8rpx;
							min-width: 24rpx;
							text-align: center;
						}
					}
				}
			}

			.weekly-stats {
				background: #fff;
				border-radius: 8rpx; // 减少圆角
				padding: 12rpx; // 减少内边距
				margin-bottom: 12rpx; // 减少底部边距

				.stats-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 16rpx 0;
					border-bottom: 1px solid #eee;

					.stats-title {
						font-size: 28rpx;
						font-weight: bold;
						color: #1a1a1a;
						position: relative;
						padding-left: 20rpx;
						white-space: nowrap;

						&::before {
							content: '';
							position: absolute;
							left: 0;
							top: 15%;
							height: 70%;
							width: 4rpx;
							background: linear-gradient(to bottom, #2979ff, #1890ff);
							border-radius: 2rpx;
						}
					}

					.date-picker {
						display: flex;
						align-items: center;
						padding: 6rpx 16rpx;
						background: #f5f7fa;
						border-radius: 20rpx;
						cursor: pointer;

						&:active {
							opacity: 0.8;
						}

						.date-text {
							font-size: 22rpx;
							color: #2979ff;
						}

						.separator {
							margin: 0 6rpx;
							color: #909399;
						}
					}
				}

				.realtime-violations {
					background: #fff;
					border-radius: 8rpx; // 减少圆角
					padding: 12rpx; // 减少内边距
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); // 减少阴影
					margin-top: 16rpx; // 减少顶部边距

					.section-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 16rpx;
						padding: 12rpx 0;
						border-bottom: 1rpx solid #f0f0f0;

						.header-left {
							display: flex;
							align-items: center;
							gap: 16rpx;

							.title {
								font-size: 28rpx;
								font-weight: bold;
								color: #1a1a1a;
								position: relative;
								padding-left: 20rpx;
								white-space: nowrap;

								&::before {
									content: '';
									position: absolute;
									left: 0;
									top: 15%;
									height: 70%;
									width: 4rpx;
									background: linear-gradient(to bottom, #2979ff, #1890ff);
									border-radius: 2rpx;
								}
							}

							.date-picker {
								display: flex;
								align-items: center;
								padding: 6rpx 12rpx;
								background: #f0f7ff;
								border-radius: 6rpx;
								border: 1px solid rgba(41, 121, 255, 0.1);
								cursor: pointer;
								transition: all 0.3s ease;

								&:hover {
									background: #e6f7ff;
									border-color: rgba(41, 121, 255, 0.2);
								}

								.date-text {
									font-size: 20rpx;
									color: #2979ff;
									font-weight: 500;
								}

								.separator {
									margin: 0 6rpx;
									color: #2979ff;
									opacity: 0.6;
								}

								.u-icon {
									margin-left: 6rpx;
								}
							}
						}
					}

					// 优化车辆状态统计卡片样式
					.violation-stats-cards {
						display: flex;
						gap: 12rpx; // 增加卡片间隙
						margin: 20rpx 0; // 增加上下边距

						.stat-card {
							flex: 1;
							border-radius: 16rpx; // 增加圆角
							padding: 24rpx 16rpx; // 增加内边距
							display: flex;
							flex-direction: column;
							align-items: center;
							text-align: center;
							transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
							position: relative;
							overflow: hidden;
							cursor: pointer;
							background: #fff;
							box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08); // 增加阴影

							&::before {
								content: '';
								position: absolute;
								bottom: 0;
								left: 50%;
								transform: translateX(-50%);
								width: 60%;
								height: 4rpx;
								border-radius: 2rpx;
								transition: all 0.3s ease;
							}

							.icon-wrapper {
								width: 56rpx; // 增加图标尺寸
								height: 56rpx;
								border-radius: 12rpx; // 增加圆角
								display: flex;
								align-items: center;
								justify-content: center;
								margin-bottom: 12rpx; // 增加底部间距
								transition: all 0.4s ease;
								position: relative;

								&::after {
									content: '';
									position: absolute;
									inset: -6rpx;
									border-radius: 20rpx;
									background: inherit;
									opacity: 0.2;
									transform: scale(0.8);
									transition: all 0.4s ease;
									z-index: -1;
								}
							}

							.stat-info {
								.count {
									font-size: 36rpx; // 增加字体大小
									font-weight: 700;
									line-height: 1;
									margin-bottom: 6rpx; // 增加间距
									font-family: "DIN Alternate", "Arial", sans-serif;
									transition: all 0.3s ease;
								}

								.label {
									font-size: 22rpx; // 增加字体大小
									font-weight: 500;
									opacity: 0.8;
									transition: all 0.3s ease;
								}
							}

							&.not-entered {
								&::before {
									background: linear-gradient(90deg, #ff4d4f, #ff7875);
								}

								.icon-wrapper {
									background: linear-gradient(135deg, #ff4d4f, #ff7875);
									color: #fff;
								}

								.stat-info {
									.count {
										color: #ff4d4f;
									}

									.label {
										color: #cf1322;
									}
								}

								&:hover {
									background: linear-gradient(145deg, #fff5f5, #fff);
									box-shadow: 0 12rpx 40rpx rgba(255, 77, 79, 0.15);

									.icon-wrapper::after {
										transform: scale(1.2);
										opacity: 0.1;
									}
								}
							}

							&.in-progress {
								&::before {
									background: linear-gradient(90deg, #1890ff, #40a9ff);
								}

								.icon-wrapper {
									background: linear-gradient(135deg, #1890ff, #40a9ff);
									color: #fff;
								}

								.stat-info {
									.count {
										color: #1890ff;
									}

									.label {
										color: #096dd9;
									}
								}

								&:hover {
									background: linear-gradient(145deg, #f0f9ff, #fff);
									box-shadow: 0 12rpx 40rpx rgba(24, 144, 255, 0.15);

									.icon-wrapper::after {
										transform: scale(1.2);
										opacity: 0.1;
									}
								}
							}

							&.has-left {
								&::before {
									background: linear-gradient(90deg, #52c41a, #73d13d);
								}

								.icon-wrapper {
									background: linear-gradient(135deg, #52c41a, #73d13d);
									color: #fff;
								}

								.stat-info {
									.count {
										color: #52c41a;
									}

									.label {
										color: #389e0d;
									}
								}

								&:hover {
									background: linear-gradient(145deg, #f6ffed, #fff);
									box-shadow: 0 12rpx 40rpx rgba(82, 196, 26, 0.15);

									.icon-wrapper::after {
										transform: scale(1.2);
										opacity: 0.1;
									}
								}
							}

							&:hover {
								transform: translateY(-6rpx) scale(1.02);

								.icon-wrapper {
									transform: scale(1.1) rotate(5deg);
								}

								&::before {
									width: 80%;
								}
							}

							&.selected {
								transform: translateY(-8rpx) scale(1.08);
								box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
								border: 3rpx solid transparent;

								&::before {
									width: 100%;
									height: 8rpx;
								}

								&::after {
									content: '✓';
									position: absolute;
									top: 8rpx;
									right: 8rpx;
									width: 32rpx;
									height: 32rpx;
									background: #52c41a;
									color: #fff;
									border-radius: 50%;
									display: flex;
									align-items: center;
									justify-content: center;
									font-size: 20rpx;
									font-weight: bold;
									animation: checkmark 0.3s ease-in-out;
								}

								.icon-wrapper {
									transform: scale(1.2);
								}

								.stat-info .count {
									font-size: 46rpx;
									font-weight: 700;
								}

								.stat-info .label {
									font-weight: 600;
								}

								&.not-entered {
									background: linear-gradient(145deg, #fff2f0, #fff5f5);
									box-shadow: 0 20rpx 60rpx rgba(255, 77, 79, 0.3);
									border-color: #ff4d4f;

									&::before {
										background: #ff4d4f;
									}

									.icon-wrapper {
										background: #ff4d4f;
									}

									.stat-info .count {
										color: #ff4d4f;
									}

									.stat-info .label {
										color: #ff4d4f;
									}
								}

								&.in-progress {
									background: linear-gradient(145deg, #e6f7ff, #f0f9ff);
									box-shadow: 0 20rpx 60rpx rgba(24, 144, 255, 0.3);
									border-color: #1890ff;

									&::before {
										background: #1890ff;
									}

									.icon-wrapper {
										background: #1890ff;
									}

									.stat-info .count {
										color: #1890ff;
									}

									.stat-info .label {
										color: #1890ff;
									}
								}

								&.has-left {
									background: linear-gradient(145deg, #f6ffed, #f9fff6);
									box-shadow: 0 20rpx 60rpx rgba(82, 196, 26, 0.3);
									border-color: #52c41a;

									&::before {
										background: #52c41a;
									}

									.icon-wrapper {
										background: #52c41a;
									}

									.stat-info .count {
										color: #52c41a;
									}

									.stat-info .label {
										color: #52c41a;
									}
								}

								@keyframes checkmark {
									0% {
										transform: scale(0) rotate(0deg);
										opacity: 0;
									}
									50% {
										transform: scale(1.2) rotate(180deg);
										opacity: 1;
									}
									100% {
										transform: scale(1) rotate(360deg);
										opacity: 1;
									}
								}
							}

							&:active {
								transform: translateY(-2rpx) scale(0.98);
								transition: all 0.1s ease;
							}
						}
					}

					.violation-cards {
						// 修改为单列布局，减少左右间距
						display: flex;
						flex-direction: column;
						gap: 16rpx;
						padding: 0 4rpx; // 大幅减少左右内边距

						// 空状态容器样式
						.empty-state-container {
							display: flex;
							justify-content: center;
							align-items: center;
							min-height: 400rpx;
							padding: 40rpx 20rpx;
						}

						.empty-state-card {
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							padding: 60rpx 40rpx;
							background: linear-gradient(145deg, #fafbfc 0%, #ffffff 100%);
							border-radius: 24rpx;
							border: 2rpx dashed #e1e4e8;
							box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.02);
							position: relative;
							overflow: hidden;
							max-width: 600rpx;
							width: 100%;

							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								background: radial-gradient(circle at center, rgba(64, 158, 255, 0.03) 0%, transparent 70%);
								pointer-events: none;
							}

							.empty-icon {
								margin-bottom: 24rpx;
								font-size: 80rpx;
								opacity: 0.7;
								animation: float 3s ease-in-out infinite;

								.icon-emoji {
									font-size: 80rpx;
								}
							}

							.empty-content {
								text-align: center;
								margin-bottom: 32rpx;

								.empty-title {
									display: block;
									font-size: 32rpx;
									font-weight: 600;
									color: #2c3e50;
									margin-bottom: 12rpx;
									line-height: 1.4;
								}

								.empty-subtitle {
									display: block;
									font-size: 26rpx;
									color: #8590a6;
									line-height: 1.5;
									max-width: 400rpx;
								}
							}

							.empty-actions {
								display: flex;
								justify-content: center;

								.action-btn {
									padding: 16rpx 32rpx;
									background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
									border-radius: 50rpx;
									box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
									transition: all 0.3s ease;

									&:active {
										transform: translateY(2rpx);
										box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.4);
									}

									.btn-text {
										color: #ffffff;
										font-size: 26rpx;
										font-weight: 500;
									}
								}
							}

							@keyframes float {
								0%, 100% {
									transform: translateY(0);
								}
								50% {
									transform: translateY(-8rpx);
								}
							}
						}

						// 紧凑的实时违规记录项
						.realtime-violation-item-compact {
							margin-bottom: 16rpx;
							background: #fff;
							border-radius: 16rpx;
							box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
							overflow: hidden;
							transition: all 0.3s ease;

							&.expanded {
								box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
							}

							// 紧凑的一行显示
							.compact-row {
								display: flex;
								align-items: center;
								padding: 24rpx 32rpx;
								cursor: pointer;
								transition: background-color 0.2s ease;

								&:active {
									background-color: #f8f9fa;
								}

								.main-info {
									flex: 1;
									display: flex;
									align-items: center;
									gap: 12rpx;
									overflow: hidden;

									// 车牌号
									.plate-number-compact {
										display: inline-block;
										font-size: 28rpx;
										font-weight: bold;
										padding: 8rpx 16rpx;
										border-radius: 8rpx;
										font-family: "微软雅黑";
										letter-spacing: 1rpx;
										min-width: 140rpx;
										text-align: center;
										flex-shrink: 0;

										&.blue-plate {
											background: linear-gradient(180deg, #0C4FC5 0%, #216FEF 100%);
											color: #FFFFFF;
											border: 1px solid #0C4FC5;
										}

										&.green-plate {
											background: linear-gradient(180deg, #6AD390 0%, #D0F1E4 100%);
											color: #000000;
											border: 1px solid #6AD390;
										}
									}

									// 状态标签
									.status-tag {
										padding: 8rpx 16rpx;
										border-radius: 16rpx;
										font-size: 24rpx;
										flex-shrink: 0;

										.status-text {
											color: #fff;
											font-weight: 500;
										}

										&.status-not-processed {
											background: #ff4d4f;
										}

										&.status-processed {
											background: #52c41a;
										}

										&.status-processing {
											background: #ff9500; // 修改为橙色，更醒目
										}

										&.status-default {
											background: #8c8c8c;
										}
									}

									// 违规原因简化显示
									.reason-short {
										font-size: 24rpx;
										color: #ff4d4f;
										flex-shrink: 0;
										max-width: 140rpx;
										overflow: hidden;
										text-overflow: ellipsis;
										white-space: nowrap;
										background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%);
										padding: 6rpx 12rpx;
										border-radius: 12rpx;
										border: 1px solid #ffccc7;
										font-weight: 500;
										text-align: center;
									}

									// 时间显示
									.time-display {
										font-size: 22rpx;
										color: #999;
										margin-left: auto;
										flex-shrink: 0;
										min-width: 100rpx;
										text-align: right;
									}
								}

								// 展开图标
								.expand-icon {
									margin-left: 16rpx;
									flex-shrink: 0;
									transition: transform 0.3s ease;
								}
							}

							// 展开后的详细内容
							.detail-content {
								padding: 0 32rpx 32rpx;
								border-top: 1px solid #f0f0f0;
								background: #fafafa;

								.detail-section {
									padding-top: 24rpx;

									// 时间信息卡片
									.time-cards {
										display: flex;
										flex-direction: column;
										gap: 16rpx;
										margin-bottom: 24rpx;

										.time-card {
											display: flex;
											align-items: center;
											padding: 16rpx 20rpx;
											background: #fff;
											border-radius: 12rpx;
											box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);

											.card-icon {
												margin-right: 16rpx;
												width: 40rpx;
												height: 40rpx;
												display: flex;
												align-items: center;
												justify-content: center;
												border-radius: 8rpx;

												.emoji-icon {
													font-size: 24rpx;
												}
											}

											.card-content {
												flex: 1;

												.card-label {
													font-size: 22rpx;
													color: #666;
													display: block;
													margin-bottom: 4rpx;
												}

												.card-time {
													font-size: 26rpx;
													color: #333;
													font-weight: 500;
												}
											}

											&.appointment-card .card-icon {
												background: rgba(41, 121, 255, 0.1);
											}

											&.enter-card .card-icon {
												background: rgba(82, 196, 26, 0.1);
											}

											&.leave-card .card-icon {
												background: rgba(255, 77, 79, 0.1);
											}
										}
									}

									// 违规详情
									.violation-detail {
										margin-bottom: 24rpx;

										.detail-row {
											display: flex;
											align-items: center;
											margin-bottom: 16rpx;
											padding: 16rpx 20rpx;
											background: #fff;
											border-radius: 12rpx;
											box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);

											.detail-tag {
												display: flex;
												align-items: center;
												margin-right: 16rpx;
												min-width: 120rpx;

												.tag-label {
													font-size: 22rpx;
													color: #666;
													margin-left: 8rpx;
												}
											}

											.detail-value {
												flex: 1;
												font-size: 26rpx;
												color: #333;

												&.status-not-processed {
													color: #ff4d4f;
												}

												&.status-processed {
													color: #52c41a;
												}

												&.status-processing {
													color: #ff9500; // 修改为橙色，与标签背景色一致
												}
											}
										}
									}

									// 操作按钮
									.action-buttons {
										display: flex;
										justify-content: center;

										.action-btn {
											display: flex;
											align-items: center;
											gap: 8rpx;
											padding: 16rpx 32rpx;
											border-radius: 12rpx;
											font-size: 26rpx;
											font-weight: 500;
											transition: all 0.2s ease;

											&.primary {
												background: #1890ff;
												color: #fff;

												&:active {
													background: #096dd9;
												}
											}
										}
									}
								}

								// 紧凑型三行布局样式
								.compact-detail-layout {
									padding-top: 24rpx;
									animation: slideIn 0.3s ease-out;

									// 第一行：时间流程
									.time-flow-row {
										display: flex;
										align-items: stretch;
										justify-content: space-between;
										margin-bottom: 16rpx;
										padding: 16rpx;
										background: #f8f9fa;
										border-radius: 12rpx;

										.time-item {
											display: flex;
											flex-direction: column;
											align-items: flex-start;
											flex: 1;
											min-width: 0;
											max-width: 33.33%;
											position: relative;
											padding: 16rpx 12rpx;
											background: #fff;
											border-radius: 12rpx;
											margin-right: 12rpx;
											box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
											border: 1px solid #f0f0f0;

											&:last-child {
												margin-right: 0;
											}

											// 时间头部（图标+标签）
											.time-header {
												display: flex;
												align-items: center;
												margin-bottom: 12rpx;

												.time-icon {
													font-size: 20rpx;
													margin-right: 8rpx;
												}

												.time-label {
													font-size: 22rpx;
													color: #999;
													white-space: nowrap;
													font-weight: 400;
												}
											}

											// 时间显示区域
											.time-display {
												display: flex;
												flex-direction: column;
												align-items: flex-start;
												width: 100%;

												.time-main {
													font-size: 32rpx;
													color: #333;
													font-weight: 600;
													line-height: 1.2;
													margin-bottom: 4rpx;
												}

												.time-date {
													font-size: 20rpx;
													color: #999;
													line-height: 1;
												}
											}

											// 不同状态的样式
											&:nth-child(1) {
												border-left: 4rpx solid #1890ff; // 预约 - 蓝色
											}

											&:nth-child(2) {
												border-left: 4rpx solid #52c41a; // 进场 - 绿色
											}

											&:nth-child(3) {
												border-left: 4rpx solid #ff9500; // 离场 - 橙色
											}
										}

										// 兼容原有的单独时间值样式
										.time-value {
											font-size: 28rpx;
											color: #333;
											font-weight: 600;
										}

										// 不同状态的图标背景色
										&:nth-child(1) .time-icon {
											background: rgba(24, 144, 255, 0.15); // 预约 - 蓝色
										}

										&:nth-child(2) .time-icon {
											background: rgba(82, 196, 26, 0.15); // 进场 - 绿色
										}

										&:nth-child(3) .time-icon {
											background: rgba(255, 149, 0, 0.15); // 离场 - 橙色
										}
									}
								}

								// 第二行：停车时长
								.duration-row {
									display: flex;
									align-items: center;
									margin-bottom: 16rpx;
									padding: 20rpx 24rpx;
									background: linear-gradient(135deg, #fff9f0 0%, #ffffff 100%);
									border-radius: 16rpx;
									box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.08);
									border: 1px solid #fff2e8;
									position: relative;
									overflow: hidden;

									// 添加左侧装饰条
									&::before {
										content: '';
										position: absolute;
										left: 0;
										top: 0;
										bottom: 0;
										width: 4rpx;
										background: linear-gradient(to bottom, #ff9500, #ffad33);
									}

									.duration-icon {
										font-size: 26rpx;
										margin-right: 12rpx;
										padding: 10rpx;
										border-radius: 50%;
										background: rgba(255, 149, 0, 0.15);
										display: flex;
										align-items: center;
										justify-content: center;
										min-width: 46rpx;
										height: 46rpx;
									}

									.duration-label {
										font-size: 26rpx;
										color: #666;
										margin-right: 12rpx;
										font-weight: 500;
									}

									.duration-value {
										font-size: 32rpx;
										font-weight: 600;
										padding: 6rpx 16rpx;
										border-radius: 20rpx;
										position: relative;

										&.duration-short {
											color: #52c41a;
											background: rgba(82, 196, 26, 0.1);
											border: 1px solid rgba(82, 196, 26, 0.2);
										}

										&.duration-medium {
											color: #ff9500;
											background: rgba(255, 149, 0, 0.1);
											border: 1px solid rgba(255, 149, 0, 0.2);
										}

										&.duration-long {
											color: #ff4d4f;
											background: rgba(255, 77, 79, 0.1);
											border: 1px solid rgba(255, 77, 79, 0.2);
											animation: pulse 2s infinite;
										}
									}
								}

								// 第三行：状态信息
								.status-row {
									display: flex;
									align-items: center;
									padding: 20rpx 24rpx;
									background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
									border-radius: 16rpx;
									box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.06);
									border: 1px solid #f0f9e8;
									position: relative;

									.status-item {
										display: flex;
										align-items: center;
										padding: 8rpx 16rpx;
										border-radius: 20rpx;
										transition: all 0.3s ease;

										.status-icon {
											font-size: 24rpx;
											margin-right: 8rpx;
										}

										.status-text {
											font-size: 28rpx;
											color: #333;
											font-weight: 500;

											&.status-not-processed {
												color: #ff4d4f;
											}

											&.status-processed {
												color: #52c41a;
											}

											&.status-processing {
												color: #ff9500;
											}
										}

										// 违规原因样式
										&:first-child {
											background: rgba(255, 77, 79, 0.08);
											border: 1px solid rgba(255, 77, 79, 0.15);

											.status-text {
												color: #ff4d4f;
												font-weight: 600;
											}
										}

										// 车辆状态样式
										&:last-child {
											background: rgba(82, 196, 26, 0.08);
											border: 1px solid rgba(82, 196, 26, 0.15);

											.status-text.status-processed {
												color: #52c41a;
												font-weight: 600;
											}

											.status-text.status-processing {
												color: #ff9500;
												background: rgba(255, 149, 0, 0.08);
												border: 1px solid rgba(255, 149, 0, 0.15);
												padding: 4rpx 12rpx;
												border-radius: 12rpx;
											}

											.status-text.status-not-processed {
												color: #ff4d4f;
												background: rgba(255, 77, 79, 0.08);
												border: 1px solid rgba(255, 77, 79, 0.15);
												padding: 4rpx 12rpx;
												border-radius: 12rpx;
											}
										}
									}

									.status-divider {
										margin: 0 20rpx;
										color: #d9d9d9;
										font-size: 28rpx;
										font-weight: 300;
									}
								}
							}
						}
					}

					.violation-card {
						width: 100%;
						background: linear-gradient(145deg, #ffffff, #f8faff);
						border-radius: 12rpx; // 稍微减少圆角
						box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
						position: relative;
						transition: all 0.3s ease;
						border: 1px solid #f0f0f0;
						overflow: hidden; // 为滑动效果准备

						// 滑动容器
						.swipe-container {
							position: relative;
							width: 100%;
							display: flex;
							transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
						}

						// 主内容区域
						.card-main {
							width: 100%;
							padding: 24rpx 20rpx 20rpx; // 减少内边距
							flex-shrink: 0;
							background: inherit;
						}



						&:hover {
							transform: translateY(-2rpx);
							box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
							border-color: #d9d9d9;
						}

						.card-header {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-top: 50rpx; // 进一步减小与顶部标签的间距
							margin-bottom: 12rpx; // 减小底部间距
							padding-bottom: 12rpx; // 减小padding
							border-bottom: 1px dashed #f0f0f0;

							.header-left {
								display: flex;
								align-items: center;
								gap: 20rpx;

								.plate-number {
									display: inline-block;
									font-size: 28rpx;
									font-weight: bold;
									padding: 8rpx 16rpx;
									border-radius: 8rpx; // 与实时违规记录保持一致
									font-family: "微软雅黑";
									letter-spacing: 1rpx;
									min-width: 160rpx;
									text-align: center;
									position: relative;
									transition: all 0.3s ease;

									&.blue-plate {
										background: linear-gradient(135deg, #0C4FC5, #216FEF);
										color: #FFFFFF;
										border: 1px solid #0C4FC5;
										box-shadow: 0 4rpx 12rpx rgba(12, 79, 197, 0.2);

										&:hover {
											transform: translateY(-2rpx);
											box-shadow: 0 6rpx 16rpx rgba(12, 79, 197, 0.3);
										}
									}

									&.green-plate {
										background: linear-gradient(180deg, #6AD390 0%, #D0F1E4 100%);
										color: #000000;
										border: 1px solid #6AD390;
										box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.2);

										&:hover {
											transform: translateY(-2rpx);
											box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.3);
										}

										&::before {
											content: '新能源';
											position: absolute;
											top: -20rpx;
											right: -10rpx;
											background: #f6ffed;
											color: #52c41a;
											font-size: 20rpx;
											padding: 2rpx 8rpx;
											border-radius: 4rpx;
											border: 1px solid #b7eb8f;
											transform: scale(0.8);
										}
									}
								}

								.time {
									font-size: 28rpx;
									color: #666;
									font-family: "DIN Alternate";
									background: #f5f5f5;
									padding: 4rpx 12rpx;
									border-radius: 6rpx;
								}
							}

							.status {
								padding: 6rpx 20rpx;
								border-radius: 20rpx;
								font-size: 26rpx;
								font-weight: 500;
								display: flex;
								align-items: center;
								gap: 6rpx;

								&::before {
									content: '';
									width: 8rpx;
									height: 8rpx;
									border-radius: 50%;
								}

								&.not-processed {
									background: #fff2e8;
									color: #fa541c;
									border: 1px solid #ffbb96;

									&::before {
										background: #fa541c;
										box-shadow: 0 0 0 4rpx rgba(250, 84, 28, 0.2);
									}
								}

								&.is-processed {
									background: #f6ffed;
									color: #52c41a;
									border: 1px solid #b7eb8f;

									&::before {
										background: #52c41a;
										box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
									}
								}

								&.processing {
									background: #e6f7ff;
									color: #1890ff;
									border: 1px solid #91d5ff;

									&::before {
										background: #1890ff;
										box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.2);
									}
								}
							}
						}

						.card-content {
							margin-top: 16rpx;
							padding: 20rpx;
							background: #fafafa;
							border-radius: 12rpx;
							border: 1px solid #f0f0f0;

							// 美观的时间卡片显示
							.time-info {
								margin: 16rpx 0; // 减少上下边距
								display: flex;
								gap: 12rpx; // 减少卡片间隙

								.time-card {
									flex: 1;
									background: linear-gradient(145deg, #ffffff, #f8faff);
									padding: 16rpx; // 减少内边距
									border-radius: 12rpx; // 减少圆角
									border: 1rpx solid #e8e8e8;
									box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04); // 减少阴影
									transition: all 0.3s ease;
									display: flex;
									align-items: center;
									gap: 12rpx; // 减少内部间隙

									.time-icon {
										display: flex;
										align-items: center;
										justify-content: center;
										width: 48rpx; // 减少图标大小
										height: 48rpx;
										border-radius: 50%;
										background: rgba(255, 255, 255, 0.8);
										box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08); // 减少阴影
									}

									.time-content {
										flex: 1;

										.time-label {
											font-size: 22rpx; // 减少字体大小
											color: #666;
											margin-bottom: 6rpx; // 减少间距
											display: block;
										}

										.time-display {
											display: flex;
											flex-direction: column;
											gap: 2rpx; // 减少间隙

											.date-text {
												font-size: 24rpx; // 减少字体大小
												color: #333;
												font-weight: 500;
											}

											.time-text {
												font-size: 28rpx; // 减少字体大小
												font-weight: 700;
												font-family: "DIN Alternate", "Arial", sans-serif;
												color: #1890ff;
											}
										}
									}

									&.enter-time {
										border-left: 4rpx solid #52c41a;

										.time-icon {
											background: rgba(82, 196, 26, 0.1);
										}

										.time-display .time-text {
											color: #52c41a;
										}
									}

									&.leave-time {
										border-left: 4rpx solid #ff7875;

										.time-icon {
											background: rgba(255, 120, 117, 0.1);
										}

										.time-display .time-text {
											color: #ff7875;
										}
									}

									&:hover {
										transform: translateY(-2rpx);
										box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

										.time-icon {
											transform: scale(1.1);
										}
									}
								}
							}

							.violation-reason {
								display: flex;
								align-items: center;
								gap: 12rpx; // 减少间隙
								margin-top: 12rpx; // 添加上边距

								.label {
									color: #666;
									font-size: 26rpx; // 减少字体大小
									font-weight: 500;
									flex-shrink: 0;
								}

								.value {
									color: #ff4d4f;
									font-size: 26rpx; // 减少字体大小
									font-weight: 600;
									background: #fff2f0;
									padding: 6rpx 16rpx; // 减少内边距
									border-radius: 6rpx;
									border-left: 3rpx solid #ff4d4f; // 减少边框宽度
									flex: 1;
								}
							}
						}

						.card-actions {
							margin-top: 20rpx;
							display: flex;
							justify-content: flex-end;
							padding-top: 16rpx;
							border-top: 1px dashed #f0f0f0;

							:deep(.u-button) {
								min-width: 160rpx;
								border-radius: 8rpx;
								font-weight: 500;

								&:active {
									transform: scale(0.98);
								}
							}
						}

						// 预约时间标签样式优化  
						.appointment-tag {
							position: absolute;
							top: 12rpx; // 减少顶部间距
							left: 16rpx; // 减少左边距
							right: 120rpx; // 增加右边距，避免遮挡状态标签
							z-index: 2;
							background: linear-gradient(135deg, #e6f7ff, #f0f7ff);
							padding: 6rpx 16rpx; // 减少内边距
							border-radius: 6rpx; // 减少圆角
							display: flex;
							align-items: center;
							gap: 6rpx; // 减少间隙

							.tag-label {
								font-size: 20rpx; // 进一步减少字体大小
								color: #2979ff;
								font-weight: bold;
								flex-shrink: 0; // 防止文字被压缩
							}

							.time {
								color: #2979ff;
								font-size: 22rpx; // 进一步减少字体大小
								font-weight: 500;
								font-family: "DIN Alternate", "Arial", sans-serif;
								overflow: hidden; // 隐藏溢出
								text-overflow: ellipsis; // 省略号
								white-space: nowrap; // 不换行
								flex: 1; // 占据剩余空间
								min-width: 0; // 允许缩小
							}
						}

						// 车辆状态标签
						.vehicle-status {
							position: absolute;
							top: 12rpx; // 与预约时间对齐
							right: 12rpx; // 进一步减少右边距
							width: auto;
							min-width: 80rpx; // 设置最小宽度确保不被挤压
							padding: 6rpx 12rpx; // 减少内边距
							border-radius: 6rpx;
							display: flex;
							align-items: center;
							justify-content: center; // 居中对齐
							gap: 4rpx; // 进一步减少间隙
							font-size: 20rpx; // 再次减少字体大小
							font-weight: 500;
							z-index: 3; // 提高层级，确保在预约时间之上
							box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08); // 减少阴影

							&.not-entered {
								background: linear-gradient(135deg, #ff4d4f, #ff7875);
								color: #fff;
								animation: pulse 2s infinite;
							}

							&.not-left {
								background: linear-gradient(135deg, #1890ff, #40a9ff);
								color: #fff;
							}

							&.entered {
								background: linear-gradient(135deg, #52c41a, #73d13d);
								color: #fff;
							}

							.u-icon {
								transform: scale(0.6); // 进一步缩小图标
								margin-right: 2rpx; // 添加少量右边距
							}
						}
					}

					@keyframes pulse {
						0% {
							transform: scale(1);
						}

						50% {
							transform: scale(1.05);
						}

						100% {
							transform: scale(1);
						}
					}

					@keyframes shake {

						0%,
						100% {
							transform: translateX(0);
						}

						25% {
							transform: translateX(-2rpx);
						}

						75% {
							transform: translateX(2rpx);
						}
					}

					@keyframes rotate {
						from {
							transform: rotate(0deg);
						}

						to {
							transform: rotate(360deg);
						}
					}

					@keyframes warning {

						0%,
						100% {
							transform: scale(1);
						}

						50% {
							transform: scale(1.1);
						}
					}

					@keyframes shimmer {
						to {
							left: 100%;
						}
					}

					@keyframes shine {
						0% {
							left: -100%;
						}

						20% {
							left: 100%;
						}

						100% {
							left: 100%;
						}
					}

					@keyframes ripple {
						to {
							transform: translate(-50%, -50%) scale(2);
							opacity: 0;
						}
					}

					@keyframes highlight {
						0% {
							transform: scale(1);
						}

						50% {
							transform: scale(1.02);
						}

						100% {
							transform: scale(1);
						}
					}

					@keyframes bounce {

						0%,
						20%,
						50%,
						80%,
						100% {
							transform: translateY(0);
						}

						40% {
							transform: translateY(-8rpx);
						}

						60% {
							transform: translateY(-4rpx);
						}
					}

					@keyframes slideDown {
						0% {
							opacity: 0;
							transform: translateY(-30rpx);
							max-height: 0;
						}

						50% {
							opacity: 0.8;
							transform: translateY(-10rpx);
						}

						100% {
							opacity: 1;
							transform: translateY(0);
							max-height: 2000rpx;
						}
					}

					@keyframes fadeInUp {
						0% {
							opacity: 0;
							transform: translate3d(0, 40rpx, 0);
						}

						100% {
							opacity: 1;
							transform: translate3d(0, 0, 0);
						}
					}

					@keyframes slideInLeft {
						0% {
							opacity: 0;
							transform: translate3d(-100%, 0, 0);
						}

						100% {
							opacity: 1;
							transform: translate3d(0, 0, 0);
						}
					}

					@keyframes timeCardPulse {
						0% {
							transform: scale(1);
							opacity: 1;
						}

						50% {
							transform: scale(1.05);
							opacity: 0.9;
						}

						100% {
							transform: scale(1);
							opacity: 1;
						}
					}

					@keyframes floatUp {
						0% {
							transform: translateY(0);
						}

						50% {
							transform: translateY(-4rpx);
						}

						100% {
							transform: translateY(0);
						}
					}
				}
			}

			.high-risk-section {
				margin-top: 16rpx;

				.section-header {
					.warning-title {
						display: flex;
						align-items: center;
						background: #fff2f0;
						padding: 16rpx;
						border-radius: 8rpx;
						margin-bottom: 16rpx;
						border: 1px solid #ffccc7;

						.u-icon {
							margin-right: 8rpx;
						}

						.title-text {
							font-size: 28rpx;
							font-weight: bold;
							color: #ff4d4f;
							margin-right: 12rpx;
						}

						.total-count {
							background: #ff4d4f;
							color: #fff;
							padding: 3rpx 12rpx;
							border-radius: 16rpx;
							font-size: 20rpx;
						}
					}
				}

				.risk-vehicles-list {
					// 高风险车辆空状态样式
					.empty-state-container {
						display: flex;
						justify-content: center;
						align-items: center;
						min-height: 300rpx;
						padding: 40rpx 20rpx;
					}

					.empty-state-card {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						padding: 50rpx 30rpx;
						background: linear-gradient(145deg, #fafbfc 0%, #ffffff 100%);
						border-radius: 20rpx;
						border: 2rpx dashed #e1e4e8;
						box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.02);
						position: relative;
						overflow: hidden;
						max-width: 500rpx;
						width: 100%;

						&::before {
							content: '';
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							background: radial-gradient(circle at center, rgba(82, 196, 26, 0.02) 0%, transparent 70%);
							pointer-events: none;
						}

						.empty-icon {
							margin-bottom: 20rpx;
							font-size: 64rpx;
							opacity: 0.7;
							animation: float 3s ease-in-out infinite;

							.icon-emoji {
								font-size: 64rpx;
							}
						}

						.empty-content {
							text-align: center;

							.empty-title {
								display: block;
								font-size: 28rpx;
								font-weight: 600;
								color: #2c3e50;
								margin-bottom: 8rpx;
								line-height: 1.4;
							}

							.empty-subtitle {
								display: block;
								font-size: 24rpx;
								color: #8590a6;
								line-height: 1.5;
								max-width: 350rpx;
							}
						}

						@keyframes float {
							0%, 100% {
								transform: translateY(0);
							}
							50% {
								transform: translateY(-6rpx);
							}
						}
					}

					.risk-vehicle-item {
						margin-bottom: 16rpx;
						border-radius: 8rpx;
						background: #fff;
						overflow: hidden;
						transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
						box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

						&.expanded {
							box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
							transform: translateY(-4rpx);

							.risk-card {
								background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
								border-bottom: 3rpx solid #1890ff;
							}

							.violation-details {
								animation: slideDown 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
							}
						}

						&:hover {
							transform: translateY(-4rpx);
							box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
						}

						.risk-card {
							padding: 12rpx 16rpx;
							background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);

							&:active {
								background: linear-gradient(to bottom, #f8f9fa 0%, #f5f6fa 100%);
							}

							.card-header {
								display: flex;
								justify-content: space-between;
								align-items: center;
								margin-bottom: 12rpx;

								.plate-info {
									display: flex;
									align-items: center;
									gap: 12rpx;

									.plate-number {
										display: inline-block;
										font-size: 28rpx; // 与实时违规记录保持一致
										font-weight: bold;
										padding: 8rpx 16rpx; // 与实时违规记录保持一致
										border-radius: 8rpx; // 与实时违规记录保持一致
										font-family: "微软雅黑";
										letter-spacing: 1rpx;
										min-width: 140rpx;
										text-align: center;

										&.blue-plate {
											background: linear-gradient(180deg, #0C4FC5 0%, #216FEF 100%);
											color: #FFFFFF;
											border: 1px solid #0C4FC5;
										}

										&.green-plate {
											background: linear-gradient(180deg, #6AD390 0%, #D0F1E4 100%);
											color: #000000;
											border: 1px solid #6AD390;
										}
									}

									.violation-badge {
										display: inline-flex;
										align-items: center;
										padding: 3rpx 8rpx;
										border-radius: 12rpx;
										margin-left: 8rpx;

										&.violation-normal {
											background: #8c8c8c; // 1-2次违规显示绿色
										}

										&.violation-warning {
											background: #faad14; // 3-4次违规显示黄色
										}

										&.violation-severe {
											background: #f5222d; // 5次及以上违规显示红色
											animation: pulse 1.5s infinite;
										}

										text {
											color: #FFFFFF;
											font-size: 24rpx;
											margin-left: 4rpx;
										}

										&.violation-severe {
											box-shadow: 0 0 12rpx rgba(245, 34, 45, 0.4);
										}
									}
								}
							}


						}

						.violation-details {
							background: linear-gradient(145deg, #ffffff 0%, #f8fafe 100%);
							border-top: none;
							padding: 16rpx;
							position: relative;
							overflow: hidden;
							border-radius: 0 0 16rpx 16rpx;
							box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.02);

							// 添加装饰背景
							&::before {
								content: '';
								position: absolute;
								top: -50%;
								right: -30%;
								width: 300rpx;
								height: 300rpx;
								background: radial-gradient(circle, rgba(41, 121, 255, 0.03) 0%, transparent 70%);
								border-radius: 50%;
								z-index: 0;
							}

							&::after {
								content: '';
								position: absolute;
								bottom: -40%;
								left: -20%;
								width: 250rpx;
								height: 250rpx;
								background: radial-gradient(circle, rgba(82, 196, 26, 0.03) 0%, transparent 70%);
								border-radius: 50%;
								z-index: 0;
							}

							// 添加顶部分割线装饰
							&:before {
								content: '';
								position: absolute;
								top: 0;
								left: 50%;
								transform: translateX(-50%);
								width: 40rpx;
								height: 4rpx;
								background: linear-gradient(90deg, #1890ff, #52c41a);
								border-radius: 2rpx;
								z-index: 1;
							}

							&>* {
								position: relative;
								z-index: 1;
							}

							// 业主信息样式（折叠面板内）
							.owner-info-collapsed {
								margin-bottom: 12rpx;
								background: linear-gradient(145deg, #ffffff, #f8faff);
								padding: 12rpx 16rpx;
								border-radius: 8rpx;
								box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
								border: 1px solid rgba(24, 144, 255, 0.08);

								.info-row {
									&:first-child {
										display: flex;
										gap: 12rpx;
										margin-bottom: 8rpx;

										.info-item {
											flex: 1;
											padding: 8rpx 12rpx;
											background: rgba(255, 255, 255, 0.8);
											border-radius: 6rpx;
											box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.02);

											.label {
												color: #8c8c8c;
												font-size: 20rpx;
												margin-bottom: 4rpx;
												display: flex;
												align-items: center;
												font-weight: 500;

												&.name-label::before {
													content: '👤';
													margin-right: 6rpx;
													font-size: 24rpx;
												}

												&.phone-label::before {
													content: '📱';
													margin-right: 6rpx;
													font-size: 24rpx;
												}
											}

											.value {
												font-size: 24rpx;
												font-weight: 600;
												color: #333;

												&.highlight {
													color: #1890ff;
													font-size: 26rpx;
												}

												&.phone {
													color: #04ab1d;
													font-family: Monaco, monospace;
													letter-spacing: 1px;
												}
											}
										}
									}

									&.address-row {
										background: rgba(255, 255, 255, 0.6);
										padding: 8rpx 12rpx;
										border-radius: 6rpx;
										display: flex;
										align-items: flex-start;

										.label {
											color: #666;
											font-size: 20rpx;
											margin-right: 8rpx;
											flex-shrink: 0;
											display: flex;
											align-items: center;
											font-weight: 500;

											&::before {
												content: '🏠';
												margin-right: 4rpx;
												font-size: 20rpx;
											}
										}

										.value.address {
											color: #333;
											font-size: 22rpx;
											flex: 1;
											line-height: 1.3;
										}
									}
								}
							}

							// 优化状态统计卡片组
							.status-stats {
								display: flex;
								gap: 12rpx;
								margin: 0 0 16rpx;
								padding: 16rpx;
								background: rgba(255, 255, 255, 0.6);
								backdrop-filter: blur(10px);
								border-radius: 20rpx;
								border: 1px solid rgba(255, 255, 255, 0.8);
								box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);

								.stat-card {
									flex: 1;
									display: flex;
									flex-direction: column;
									align-items: center;
									text-align: center;
									padding: 20rpx 16rpx;
									border-radius: 16rpx;
									transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
									position: relative;
									overflow: hidden;
									cursor: pointer;
									background: rgba(255, 255, 255, 0.9);
									backdrop-filter: blur(20px);
									border: 1px solid rgba(255, 255, 255, 0.2);
									box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

									// 光晕效果
									&::before {
										content: '';
										position: absolute;
										top: 0;
										left: 0;
										right: 0;
										height: 2rpx;
										border-radius: 1rpx;
										transition: all 0.4s ease;
										transform: scaleX(0);
										transform-origin: center;
									}

									// 悬浮光效
									&::after {
										content: '';
										position: absolute;
										top: -50%;
										left: -50%;
										width: 200%;
										height: 200%;
										background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
										border-radius: 50%;
										transition: all 0.6s ease;
										opacity: 0;
										transform: rotate(0deg);
									}

									.icon-wrapper {
										width: 56rpx;
										height: 56rpx;
										border-radius: 16rpx;
										display: flex;
										align-items: center;
										justify-content: center;
										margin-bottom: 12rpx;
										transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
										position: relative;
										backdrop-filter: blur(10px);
										box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

										&::before {
											content: '';
											position: absolute;
											inset: -4rpx;
											border-radius: 24rpx;
											background: inherit;
											opacity: 0.3;
											transform: scale(0.8);
											transition: all 0.4s ease;
											z-index: -1;
										}
									}

									.stat-info {
										display: flex;
										flex-direction: column;
										align-items: center;

										.label {
											font-size: 22rpx;
											font-weight: 600;
											opacity: 0.9;
											margin-bottom: 6rpx;
											transition: all 0.3s ease;
										}

										.count {
											font-size: 36rpx;
											font-weight: 800;
											font-family: "DIN Alternate", "Arial", sans-serif;
											line-height: 1;
											transition: all 0.3s ease;
											background: linear-gradient(135deg, currentColor, currentColor);
											-webkit-background-clip: text;
											position: relative;

											&::after {
												content: '';
												position: absolute;
												bottom: -4rpx;
												left: 50%;
												transform: translateX(-50%);
												width: 0;
												height: 2rpx;
												background: currentColor;
												border-radius: 1rpx;
												transition: width 0.3s ease;
											}
										}
									}

									// 不同状态的样式
									&.not-entered {
										&::before {
											background: linear-gradient(90deg, #ff4d4f, #ff7875);
										}

										.icon-wrapper {
											background: linear-gradient(135deg, #ff4d4f, #ff7875);
											color: #fff;
											box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);
										}

										.stat-info {

											.count,
											.label {
												color: #ff4d4f;
											}
										}

										&:hover {
											background: linear-gradient(145deg, rgba(255, 245, 245, 0.95), rgba(255, 255, 255, 0.95));
											border-color: rgba(255, 77, 79, 0.2);
											box-shadow: 0 12rpx 40rpx rgba(255, 77, 79, 0.15);
										}
									}

									&.in-progress {
										&::before {
											background: linear-gradient(90deg, #1890ff, #40a9ff);
										}

										.icon-wrapper {
											background: linear-gradient(135deg, #1890ff, #40a9ff);
											color: #fff;
											box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
										}

										.stat-info {

											.count,
											.label {
												color: #1890ff;
											}
										}

										&:hover {
											background: linear-gradient(145deg, rgba(240, 249, 255, 0.95), rgba(255, 255, 255, 0.95));
											border-color: rgba(24, 144, 255, 0.2);
											box-shadow: 0 12rpx 40rpx rgba(24, 144, 255, 0.15);
										}
									}

									&.has-left {
										&::before {
											background: linear-gradient(90deg, #52c41a, #73d13d);
										}

										.icon-wrapper {
											background: linear-gradient(135deg, #52c41a, #73d13d);
											color: #fff;
											box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
										}

										.stat-info {

											.count,
											.label {
												color: #52c41a;
											}
										}

										&:hover {
											background: linear-gradient(145deg, rgba(246, 255, 237, 0.95), rgba(255, 255, 255, 0.95));
											border-color: rgba(82, 196, 26, 0.2);
											box-shadow: 0 12rpx 40rpx rgba(82, 196, 26, 0.15);
										}
									}

									// 悬停效果
									&:hover {
										transform: translateY(-8rpx) scale(1.02);

										&::before {
											transform: scaleX(1);
										}

										&::after {
											opacity: 1;
											transform: rotate(180deg);
										}

										.icon-wrapper {
											transform: scale(1.15) rotate(5deg);

											&::before {
												transform: scale(1.2);
												opacity: 0.2;
											}
										}

										.stat-info .count::after {
											width: 100%;
										}
									}

									// 选中状态
									&.selected {
										transform: translateY(-12rpx) scale(1.08);
										box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
										border: 2rpx solid transparent;

										&::before {
											transform: scaleX(1);
											height: 6rpx;
										}

										&::after {
											content: '✓';
											position: absolute;
											top: 6rpx;
											right: 6rpx;
											width: 24rpx;
											height: 24rpx;
											background: #52c41a;
											color: #fff;
											border-radius: 50%;
											display: flex;
											align-items: center;
											justify-content: center;
											font-size: 16rpx;
											font-weight: bold;
											animation: checkmark 0.3s ease-in-out;
											z-index: 10;
										}

										.icon-wrapper {
											transform: scale(1.25) rotate(10deg);
											box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.2);

											&::before {
												transform: scale(1.4);
												opacity: 0.15;
											}
										}

										.stat-info {
											.count {
												font-size: 48rpx;
												font-weight: 700;

												&::after {
													width: 120%;
													height: 3rpx;
												}
											}

											.label {
												opacity: 1;
												font-weight: 700;
											}
										}

										// 不同状态的选中样式
										&.not-entered {
											border-color: #ff4d4f;
											background: linear-gradient(145deg, #fff2f0, #fff5f5);
											box-shadow: 0 20rpx 60rpx rgba(255, 77, 79, 0.25);

											&::before {
												background: #ff4d4f;
											}
										}

										&.in-progress {
											border-color: #1890ff;
											background: linear-gradient(145deg, #e6f7ff, #f0f9ff);
											box-shadow: 0 20rpx 60rpx rgba(24, 144, 255, 0.25);

											&::before {
												background: #1890ff;
											}
										}

										&.has-left {
											border-color: #52c41a;
											background: linear-gradient(145deg, #f6ffed, #f9fff6);
											box-shadow: 0 20rpx 60rpx rgba(82, 196, 26, 0.25);

											&::before {
												background: #52c41a;
											}
										}

										@keyframes checkmark {
											0% {
												transform: scale(0) rotate(0deg);
												opacity: 0;
											}
											50% {
												transform: scale(1.2) rotate(180deg);
												opacity: 1;
											}
											100% {
												transform: scale(1) rotate(360deg);
												opacity: 1;
											}
										}
									}

									// 点击效果
									&:active {
										transform: translateY(-4rpx) scale(0.98);
										transition: all 0.1s ease;
									}
								}
							}

							// 预约时间标注
							.appointment-tag {
								right: 100rpx; // 缩短右侧长度，为状态标签留出空间
								background: linear-gradient(135deg, #e6f7ff, #f0f7ff);
								padding: 6rpx 16rpx;
								border-radius: 6rpx;
								display: flex;
								align-items: center;
								gap: 6rpx;

								.tag-label {
									font-size: 20rpx;
									color: #2979ff;
									font-weight: bold;
								}

								.time {
									color: #2979ff;
									font-size: 22rpx;
									font-weight: 500;
									font-family: "DIN Alternate", "Arial", sans-serif;
								}
							}

							.card-header {
								margin-top: 50rpx; // 调整与预约时间的间距
								// ... existing code ...
							}

							.card-content {
								margin-top: 12rpx;
								padding: 16rpx;
								background: #fafafa;
								border-radius: 8rpx;
								border: 1px solid #f0f0f0;

								// 美观的时间卡片显示
								.time-info {
									margin: 20rpx 0;
									display: flex;
									gap: 16rpx;
									position: relative;

									// 连接线动画
									&::before {
										content: '';
										position: absolute;
										top: 50%;
										left: 50%;
										transform: translate(-50%, -50%);
										width: 60rpx;
										height: 4rpx;
										background: linear-gradient(90deg, transparent, #e8e8e8, transparent);
										border-radius: 2rpx;
										z-index: 1;
									}

									.time-card {
										flex: 1;
										background: linear-gradient(145deg, #ffffff 0%, #fafcff 100%);
										padding: 20rpx 16rpx;
										border-radius: 16rpx;
										border: 1rpx solid rgba(255, 255, 255, 0.6);
										box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
										transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
										display: flex;
										align-items: center;
										gap: 12rpx;
										position: relative;
										overflow: hidden;
										backdrop-filter: blur(20rpx);

										// 装饰性光效背景
										&::before {
											content: '';
											position: absolute;
											top: -50%;
											right: -50%;
											width: 200%;
											height: 200%;
											background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.05), transparent);
											border-radius: 50%;
											animation: rotate 20s linear infinite;
											z-index: 0;
										}

										// 顶部装饰线
										&::after {
											content: '';
											position: absolute;
											top: 0;
											left: 0;
											right: 0;
											height: 6rpx;
											border-radius: 24rpx 24rpx 0 0;
											transition: all 0.3s ease;
											z-index: 1;
										}

										.time-icon {
											display: flex;
											align-items: center;
											justify-content: center;
											width: 64rpx;
											height: 64rpx;
											border-radius: 16rpx;
											background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
											box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
											position: relative;
											z-index: 2;
											transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

											// 内部光晕
											&::before {
												content: '';
												position: absolute;
												inset: 8rpx;
												border-radius: 16rpx;
												background: inherit;
												opacity: 0.3;
												transition: all 0.4s ease;
											}

											.emoji-icon {
												font-size: 40rpx;
												line-height: 1;
												display: block;
												text-align: center;
												animation: pulse 3s infinite;
												filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
											}
										}

										.time-content {
											flex: 1;
											position: relative;
											z-index: 2;

											.time-label {
												font-size: 24rpx;
												color: #8c8c8c;
												margin-bottom: 8rpx;
												display: flex;
												align-items: center;
												font-weight: 600;
												letter-spacing: 0.5px;
												position: relative;

												// 小图标装饰
												&::before {
													content: '';
													width: 6rpx;
													height: 6rpx;
													border-radius: 50%;
													margin-right: 8rpx;
													transition: all 0.3s ease;
												}
											}

											.time-display {
												display: flex;
												flex-direction: column;
												gap: 4rpx;

												.date-text {
													font-size: 24rpx;
													color: #595959;
													font-weight: 500;
													font-family: -apple-system, BlinkMacSystemFont, sans-serif;
													letter-spacing: 0.3px;
												}

												.time-text {
													font-size: 32rpx;
													font-weight: 800;
													font-family: "DIN Alternate", "SF Pro Display", "Arial", sans-serif;
													letter-spacing: 1px;
													position: relative;
													display: inline-block;
													transition: all 0.3s ease;

													// 时间文字下方装饰线
													&::after {
														content: '';
														position: absolute;
														bottom: -4rpx;
														left: 0;
														width: 0;
														height: 3rpx;
														border-radius: 2rpx;
														transition: width 0.3s ease;
													}
												}
											}
										}

										// 进场时间样式
										&.enter-time {
											&::after {
												background: linear-gradient(90deg, #52c41a, #73d13d);
											}

											.time-icon {
												background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
												box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.2);
												border: 2rpx solid rgba(82, 196, 26, 0.1);

												&::before {
													background: linear-gradient(135deg, #52c41a, #73d13d);
													opacity: 0.1;
												}

												.emoji-icon {
													color: #52c41a;
													text-shadow: 0 2rpx 4rpx rgba(82, 196, 26, 0.2);
												}
											}

											.time-content {
												.time-label {
													color: #52c41a;

													&::before {
														background: linear-gradient(135deg, #52c41a, #73d13d);
														box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.3);
													}
												}

												.time-display .time-text {
													color: #52c41a;
													background: linear-gradient(135deg, #52c41a, #389e0d);
													-webkit-background-clip: text;
													color: transparent;
													text-shadow: 0 2rpx 4rpx rgba(82, 196, 26, 0.1);

													&::after {
														background: linear-gradient(90deg, #52c41a, #73d13d);
													}
												}
											}

											&:hover {
												background: linear-gradient(145deg, #f6ffed 0%, #ffffff 100%);
												border-color: rgba(82, 196, 26, 0.3);
												box-shadow: 0 16rpx 48rpx rgba(82, 196, 26, 0.15);

												.time-content .time-display .time-text::after {
													width: 100%;
												}
											}
										}

										// 离场时间样式
										&.leave-time {
											&::after {
												background: linear-gradient(90deg, #ff7875, #ff4d4f);
											}

											.time-icon {
												background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
												box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.2);
												border: 2rpx solid rgba(255, 77, 79, 0.1);

												&::before {
													background: linear-gradient(135deg, #ff7875, #ff4d4f);
													opacity: 0.1;
												}

												.emoji-icon {
													color: #ff4d4f;
													text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.2);
												}
											}

											.time-content {
												.time-label {
													color: #ff4d4f;

													&::before {
														background: linear-gradient(135deg, #ff7875, #ff4d4f);
														box-shadow: 0 0 8rpx rgba(255, 77, 79, 0.3);
													}
												}

												.time-display .time-text {
													color: #ff4d4f;
													background: linear-gradient(135deg, #ff7875, #cf1322);
													-webkit-background-clip: text;
													color: transparent;
													text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.1);

													&::after {
														background: linear-gradient(90deg, #ff7875, #ff4d4f);
													}
												}
											}

											&:hover {
												background: linear-gradient(145deg, #fff2f0 0%, #ffffff 100%);
												border-color: rgba(255, 77, 79, 0.3);
												box-shadow: 0 16rpx 48rpx rgba(255, 77, 79, 0.15);

												.time-content .time-display .time-text::after {
													width: 100%;
												}
											}
										}

										// 悬停效果
										&:hover {
											transform: translateY(-8rpx) scale(1.02);
											box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);

											.time-icon {
												transform: scale(1.1) rotate(5deg);

												&::before {
													inset: 4rpx;
													opacity: 0.2;
												}

												.emoji-icon {
													animation: bounce 0.6s ease-in-out;
												}
											}

											&::before {
												animation-duration: 10s;
											}
										}

										// 点击效果
										&:active {
											transform: translateY(-4rpx) scale(0.98);
											transition: all 0.1s ease;
										}
									}
								}

								.violation-reason {
									// ... existing code ...
								}
							}

							// 车辆状态标签移到底部
							.vehicle-status {
								position: absolute;
								top: 12rpx;
								right: 16rpx;
								width: auto;
								padding: 4rpx 12rpx;
								border-radius: 4rpx;
								display: flex;
								align-items: center;
								gap: 6rpx;
								font-size: 20rpx;
								font-weight: 500;

								&.not-entered {
									background: #ff4d4f;
									color: #fff;
								}

								&.not-left {
									background: #1890ff;
									color: #fff;
								}

								&.entered {
									background: #52c41a;
									color: #fff;
								}

								.u-icon {
									transform: scale(0.7);
								}
							}

							.violation-item {
								margin-bottom: 20rpx;
								padding: 20rpx;
								background: linear-gradient(145deg, #ffffff 0%, #fafcff 100%);
								border-radius: 16rpx;
								box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
								position: relative;
								margin-top: 16rpx;
								border: 1px solid rgba(255, 255, 255, 0.8);
								backdrop-filter: blur(20rpx);
								overflow: hidden;
								transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

								// 装饰背景
								&::before {
									content: '';
									position: absolute;
									top: -50%;
									right: -50%;
									width: 200rpx;
									height: 200rpx;
									background: radial-gradient(circle, rgba(255, 77, 79, 0.05) 0%, transparent 70%);
									border-radius: 50%;
									z-index: 0;
								}

								&::after {
									content: '';
									position: absolute;
									bottom: -30%;
									left: -30%;
									width: 150rpx;
									height: 150rpx;
									background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 70%);
									border-radius: 50%;
									z-index: 0;
								}

								// 悬停效果
								&:hover {
									transform: translateY(-6rpx);
									box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
									border-color: rgba(24, 144, 255, 0.2);
								}

								// 左侧装饰线
								&:before {
									content: '';
									position: absolute;
									left: 0;
									top: 0;
									bottom: 0;
									width: 6rpx;
									background: linear-gradient(to bottom, #ff4d4f, #1890ff, #52c41a);
									border-radius: 0 3rpx 3rpx 0;
									z-index: 1;
								}

								&>* {
									position: relative;
									z-index: 1;
								}

								/* 预约时间和状态同行布局 */
								.appointment-status-row {
									display: flex;
									justify-content: space-between;
									align-items: center;
									margin-bottom: 12rpx;
									padding: 0 2rpx;
								}

								.appointment-tag {
									display: flex;
									align-items: center;
									gap: 6rpx;

									.tag-label {
										font-size: 22rpx;
										color: #666;
										font-weight: 500;
									}

									.time {
										font-size: 24rpx;
										color: #2979ff;
										font-weight: 600;
									}
								}

								.status-badge {
									display: flex;
									align-items: center;
									gap: 4rpx;
									padding: 6rpx 12rpx;
									border-radius: 16rpx;
									font-size: 20rpx;
									font-weight: 500;

									&.not-entered {
										background: linear-gradient(135deg, #ff4d4f, #ff7875);
										color: white;
									}

									&.in-progress {
										background: linear-gradient(135deg, #faad14, #ffc53d);
										color: white;
									}

									&.has-left {
										background: linear-gradient(135deg, #52c41a, #73d13d);
										color: white;
									}
								}

								.violation-content {
									margin-top: 8rpx;

									.info-row {
										display: flex;
										gap: 8rpx;
										flex-wrap: wrap;

										.info-tag {
											display: flex;
											align-items: center;
											gap: 6rpx;
											padding: 8rpx 12rpx;
											background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
											border-radius: 12rpx;
											border: 1px solid rgba(0, 0, 0, 0.06);
											transition: all 0.3s ease;
											min-width: 180rpx;

											&:hover {
												transform: translateY(-2rpx);
												box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
											}

											&.violation-tag {
												border-left: 3rpx solid #ff4d4f;

												&:hover {
													background: linear-gradient(90deg, #fff5f5 0%, #ffffff 100%);
												}
											}

											&.reason-tag {
												border-left: 3rpx solid #2979ff;

												&:hover {
													background: linear-gradient(90deg, #f0f7ff 0%, #ffffff 100%);
												}
											}

											.tag-label {
												font-size: 20rpx;
												color: #666;
												font-weight: 500;
												min-width: 70rpx;
											}

											.tag-value {
												font-size: 22rpx;
												color: #333;
												font-weight: 600;
												flex: 1;
											}
										}
									}
								}
							}

							// 违规类型统计样式优化
							.violation-summary {
								margin-bottom: 32rpx;
								padding: 20rpx;
								background: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);
								border-radius: 16rpx;
								border: 1px solid rgba(24, 144, 255, 0.1);
								box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
								position: relative;
								overflow: hidden;

								&::before {
									content: '';
									position: absolute;
									top: 0;
									left: 0;
									right: 0;
									height: 4rpx;
									background: linear-gradient(90deg, #1890ff, #52c41a, #faad14, #ff4d4f);
									border-radius: 4rpx 4rpx 0 0;
								}

								&::after {
									content: '';
									position: absolute;
									bottom: -30%;
									right: -30%;
									width: 120rpx;
									height: 120rpx;
									background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 70%);
									border-radius: 50%;
								}

								.summary-item {
									display: inline-flex;
									align-items: center;
									padding: 12rpx 16rpx;
									margin: 8rpx 6rpx;
									background: linear-gradient(135deg, #ffffff 0%, #fafcff 100%);
									border-radius: 12rpx;
									box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
									border: 1px solid rgba(255, 255, 255, 0.8);
									position: relative;
									overflow: hidden;
									transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
									backdrop-filter: blur(10rpx);

									&::before {
										content: '';
										position: absolute;
										top: 0;
										left: 0;
										width: 4rpx;
										height: 100%;
										background: linear-gradient(to bottom, #ff4d4f, #faad14);
										border-radius: 0 2rpx 2rpx 0;
										opacity: 0.8;
									}

									&::after {
										content: '';
										position: absolute;
										top: -100%;
										left: -100%;
										width: 200%;
										height: 200%;
										background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
										transition: all 0.6s ease;
										transform: rotate(45deg);
									}

									&:hover {
										transform: translateY(-4rpx) scale(1.02);
										box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
										border-color: rgba(255, 77, 79, 0.2);

										&::after {
											top: -50%;
											left: -50%;
										}

										.type {
											color: #333;
										}

										.count {
											transform: scale(1.1);
										}
									}

									.type {
										color: #666;
										font-size: 24rpx;
										font-weight: 500;
										transition: all 0.3s ease;
										margin-right: 8rpx;
										position: relative;

										&::after {
											content: '';
											position: absolute;
											bottom: -2rpx;
											left: 0;
											width: 0;
											height: 2rpx;
											background: currentColor;
											transition: width 0.3s ease;
										}
									}

									.count {
										background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
										color: #fff;
										font-weight: 700;
										font-size: 22rpx;
										padding: 4rpx 12rpx;
										border-radius: 16rpx;
										min-width: 40rpx;
										text-align: center;
										font-family: "DIN Alternate", "Arial", sans-serif;
										box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
										transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
										position: relative;

										&::before {
											content: '';
											position: absolute;
											inset: -2rpx;
											border-radius: 22rpx;
											background: linear-gradient(135deg, #ff4d4f, #ff7875);
											opacity: 0;
											transition: all 0.3s ease;
											z-index: -1;
										}
									}

									&:hover .type::after {
										width: 100%;
									}

									&:hover .count::before {
										opacity: 0.3;
										inset: -4rpx;
									}
								}

								// 无记录提示样式
								.no-records-tip {
									display: flex;
									flex-direction: column;
									align-items: center;
									justify-content: center;
									padding: 40rpx 20rpx;
									margin: 20rpx 0;
									background: linear-gradient(145deg, #fafbfc 0%, #ffffff 100%);
									border-radius: 20rpx;
									border: 2rpx dashed #e1e4e8;
									box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.02);
									position: relative;
									overflow: hidden;

									&::before {
										content: '';
										position: absolute;
										top: 0;
										left: 0;
										right: 0;
										bottom: 0;
										background: radial-gradient(circle at center, rgba(64, 158, 255, 0.02) 0%, transparent 70%);
										pointer-events: none;
									}

									.tip-icon {
										margin-bottom: 16rpx;
										font-size: 48rpx;
										opacity: 0.7;
										animation: float 3s ease-in-out infinite;

										.icon-emoji {
											font-size: 48rpx;
										}
									}

									.tip-text {
										color: #8590a6;
										font-size: 26rpx;
										text-align: center;
										line-height: 1.5;
										font-weight: 400;
									}

									@keyframes float {
										0%, 100% {
											transform: translateY(0);
										}
										50% {
											transform: translateY(-4rpx);
										}
									}
								}
							}
						}
					}

					@keyframes pulse {
						0% {
							transform: scale(1);
						}

						50% {
							transform: scale(1.05);
						}

						100% {
							transform: scale(1);
						}
					}

					@keyframes shake {

						0%,
						100% {
							transform: translateX(0);
						}

						25% {
							transform: translateX(-2rpx);
						}

						75% {
							transform: translateX(2rpx);
						}
					}

					@keyframes rotate {
						from {
							transform: rotate(0deg);
						}

						to {
							transform: rotate(360deg);
						}
					}

					@keyframes warning {

						0%,
						100% {
							transform: scale(1);
						}

						50% {
							transform: scale(1.1);
						}
					}

					@keyframes shimmer {
						to {
							left: 100%;
						}
					}

					@keyframes shine {
						0% {
							left: -100%;
						}

						20% {
							left: 100%;
						}

						100% {
							left: 100%;
						}
					}

					@keyframes ripple {
						to {
							transform: translate(-50%, -50%) scale(2);
							opacity: 0;
						}
					}

					@keyframes highlight {
						0% {
							transform: scale(1);
						}

						50% {
							transform: scale(1.02);
						}

						100% {
							transform: scale(1);
						}
					}

					@keyframes bounce {

						0%,
						20%,
						50%,
						80%,
						100% {
							transform: translateY(0);
						}

						40% {
							transform: translateY(-8rpx);
						}

						60% {
							transform: translateY(-4rpx);
						}
					}
				}
			}
		}
	}

	.vehicle-timeline {
		margin: 32rpx 0;
		padding: 32rpx;
		background: linear-gradient(145deg, #ffffff 0%, #fafcff 100%);
		border-radius: 24rpx;
		position: relative;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(20rpx);
		overflow: hidden;

		// 装饰背景
		&::before {
			content: '';
			position: absolute;
			top: -50%;
			right: -50%;
			width: 200%;
			height: 200%;
			background: conic-gradient(from 0deg, transparent, rgba(24, 144, 255, 0.03), transparent);
			border-radius: 50%;
			animation: rotate 30s linear infinite;
			z-index: 0;
		}

		&>* {
			position: relative;
			z-index: 1;
		}

		// 状态标识样式
		.status-badge {
			display: flex;
			align-items: center;
			padding: 12rpx 24rpx;
			border-radius: 32rpx;
			font-size: 28rpx;
			font-weight: 600;
			gap: 12rpx;
			width: fit-content;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
			margin-bottom: 24rpx;
			transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
			letter-spacing: 0.5px;
			position: relative;
			overflow: hidden;

			// 光效背景
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
				transition: 0.5s;
			}

			&:hover::before {
				left: 100%;
			}

			&.not-entered {
				background: linear-gradient(135deg, #ff4d4f, #ff7875);
				color: #fff;
				box-shadow: 0 8rpx 32rpx rgba(255, 77, 79, 0.3);

				&:hover {
					transform: translateY(-2rpx);
					box-shadow: 0 12rpx 40rpx rgba(255, 77, 79, 0.4);
				}
			}

			&.in-progress {
				background: linear-gradient(135deg, #1890ff, #40a9ff);
				color: #fff;
				box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.3);

				&:hover {
					transform: translateY(-2rpx);
					box-shadow: 0 12rpx 40rpx rgba(24, 144, 255, 0.4);
				}
			}

			&.has-left {
				background: linear-gradient(135deg, #52c41a, #73d13d);
				color: #fff;
				box-shadow: 0 8rpx 32rpx rgba(82, 196, 26, 0.3);

				&:hover {
					transform: translateY(-2rpx);
					box-shadow: 0 12rpx 40rpx rgba(82, 196, 26, 0.4);
				}
			}

			.u-icon {
				margin-right: 8rpx;
				filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
			}
		}

		// 新的时间线卡片样式
		.timeline-cards {
			margin: 32rpx 0;
			display: flex;
			gap: 20rpx;
			position: relative;

			&.compact {
				margin: 16rpx 0;
				gap: 12rpx;
			}

			// 连接线
			&::before {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 60rpx;
				height: 4rpx;
				background: linear-gradient(90deg, transparent, #e8e8e8, transparent);
				border-radius: 2rpx;
				z-index: 1;
			}

			.timeline-card {
				flex: 1;
				background: linear-gradient(145deg, #ffffff 0%, #fafcff 100%);
				padding: 24rpx 20rpx;
				border-radius: 20rpx;
				border: 2rpx solid rgba(255, 255, 255, 0.6);
				box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
				transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
				display: flex;
				align-items: center;
				gap: 16rpx;
				position: relative;
				overflow: hidden;
				backdrop-filter: blur(10rpx);

				// 装饰背景
				&::before {
					content: '';
					position: absolute;
					top: -50%;
					right: -50%;
					width: 200%;
					height: 200%;
					background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.05), transparent);
					border-radius: 50%;
					animation: rotate 25s linear infinite;
					z-index: 0;
				}

				// 顶部装饰线
				&::after {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 4rpx;
					border-radius: 20rpx 20rpx 0 0;
					transition: all 0.3s ease;
					z-index: 1;
				}

				.card-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 64rpx;
					height: 64rpx;
					border-radius: 16rpx;
					background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
					box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
					position: relative;
					z-index: 2;
					transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

					&::before {
						content: '';
						position: absolute;
						inset: 6rpx;
						border-radius: 10rpx;
						background: inherit;
						opacity: 0.4;
						transition: all 0.4s ease;
					}

					.emoji-icon {
						font-size: 32rpx;
						line-height: 1;
						display: block;
						text-align: center;
						animation: pulse 2.5s infinite;
						filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
					}
				}

				.card-content {
					flex: 1;
					position: relative;
					z-index: 2;

					.card-label {
						font-size: 24rpx;
						color: #8c8c8c;
						margin-bottom: 8rpx;
						display: block;
						font-weight: 600;
						letter-spacing: 0.5px;
					}

					.card-time {
						display: flex;
						flex-direction: column;
						gap: 4rpx;

						.date-part {
							font-size: 24rpx;
							color: #595959;
							font-weight: 500;
							font-family: -apple-system, BlinkMacSystemFont, sans-serif;
						}

						.time-part {
							font-size: 32rpx;
							font-weight: 800;
							font-family: "DIN Alternate", "SF Pro Display", "Arial", sans-serif;
							letter-spacing: 0.5px;
							position: relative;
							display: inline-block;
							transition: all 0.3s ease;

							&::after {
								content: '';
								position: absolute;
								bottom: -2rpx;
								left: 0;
								width: 0;
								height: 2rpx;
								border-radius: 1rpx;
								transition: width 0.3s ease;
							}
						}
					}
				}

				// 进场卡片样式
				&.enter-card {
					&::after {
						background: linear-gradient(90deg, #52c41a, #73d13d);
					}

					.card-icon {
						background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
						box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.15);
						border: 1rpx solid rgba(82, 196, 26, 0.1);

						&::before {
							background: linear-gradient(135deg, #52c41a, #73d13d);
							opacity: 0.1;
						}

						.emoji-icon {
							color: #52c41a;
							text-shadow: 0 2rpx 4rpx rgba(82, 196, 26, 0.2);
						}
					}

					.card-content {
						.card-label {
							color: #52c41a;
						}

						.card-time .time-part {
							color: #52c41a;
							background: linear-gradient(135deg, #52c41a, #389e0d);
							-webkit-background-clip: text;
							color: transparent;

							&::after {
								background: linear-gradient(90deg, #52c41a, #73d13d);
							}
						}
					}

					&:hover {
						background: linear-gradient(145deg, #f6ffed 0%, #ffffff 100%);
						border-color: rgba(82, 196, 26, 0.2);
						box-shadow: 0 12rpx 36rpx rgba(82, 196, 26, 0.12);

						.card-content .card-time .time-part::after {
							width: 100%;
						}
					}
				}

				// 离场卡片样式
				&.leave-card {
					&::after {
						background: linear-gradient(90deg, #ff7875, #ff4d4f);
					}

					.card-icon {
						background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
						box-shadow: 0 6rpx 20rpx rgba(255, 77, 79, 0.15);
						border: 1rpx solid rgba(255, 77, 79, 0.1);

						&::before {
							background: linear-gradient(135deg, #ff7875, #ff4d4f);
							opacity: 0.1;
						}

						.emoji-icon {
							color: #ff4d4f;
							text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.2);
						}
					}

					.card-content {
						.card-label {
							color: #ff4d4f;
						}

						.card-time .time-part {
							color: #ff4d4f;
							background: linear-gradient(135deg, #ff7875, #cf1322);
							-webkit-background-clip: text;
							color: transparent;

							&::after {
								background: linear-gradient(90deg, #ff7875, #ff4d4f);
							}
						}
					}

					&:hover {
						background: linear-gradient(145deg, #fff2f0 0%, #ffffff 100%);
						border-color: rgba(255, 77, 79, 0.2);
						box-shadow: 0 12rpx 36rpx rgba(255, 77, 79, 0.12);

						.card-content .card-time .time-part::after {
							width: 100%;
						}
					}
				}

				// 悬停效果
				&:hover {
					transform: translateY(-6rpx) scale(1.02);
					box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);

					.card-icon {
						transform: scale(1.1) rotate(3deg);

						&::before {
							inset: 3rpx;
							opacity: 0.2;
						}

						.emoji-icon {
							animation: bounce 0.6s ease-in-out;
						}
					}

					&::before {
						animation-duration: 15s;
					}
				}

				// 点击效果
				&:active {
					transform: translateY(-3rpx) scale(0.98);
					transition: all 0.1s ease;
				}
			}
		}

		.timeline-container {
			margin-top: 20rpx;
			position: relative;

			.timeline-item {
				position: relative;
				padding-left: 40rpx;
				margin-bottom: 30rpx;
				display: flex;
				align-items: flex-start;

				&:last-child {
					margin-bottom: 0;
				}

				.time-dot {
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 12rpx;
					height: 12rpx;
					border-radius: 50%;
					z-index: 2;

					&::after {
						content: '';
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						width: 24rpx;
						height: 24rpx;
						border-radius: 50%;
						border: 2rpx solid;
						opacity: 0.2;
						animation: ripple 1.5s infinite;
					}
				}

				&.enter {
					.time-dot {
						background: #52c41a;
						box-shadow: 0 0 10rpx rgba(82, 196, 26, 0.3);

						&::after {
							border-color: #52c41a;
						}
					}

					.time-content {
						border-left: 3rpx solid #52c41a;

						.time-label {
							color: #52c41a;

							&::before {
								content: '⬇️';
								margin-right: 8rpx;
							}
						}

						.time-value {
							.time-part {
								background: rgba(82, 196, 26, 0.1);
								color: #52c41a;
							}
						}
					}
				}

				&.leave {
					.time-dot {
						background: #722ed1;
						box-shadow: 0 0 10rpx rgba(114, 46, 209, 0.3);

						&::after {
							border-color: #722ed1;
						}
					}

					.time-content {
						border-left: 3rpx solid #722ed1;

						.time-label {
							color: #722ed1;

							&::before {
								content: '⬆️';
								margin-right: 8rpx;
							}
						}

						.time-value {
							.time-part {
								background: rgba(114, 46, 209, 0.1);
								color: #722ed1;
							}
						}
					}
				}

				.time-content {
					flex: 1;
					background: #fff;
					padding: 16rpx 24rpx;
					border-radius: 8rpx;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
					transition: all 0.3s ease;
					position: relative;

					&:hover {
						transform: translateX(4rpx);
						box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
					}

					.time-label {
						font-size: 26rpx;
						font-weight: 600;
						margin-bottom: 12rpx;
						display: flex;
						align-items: center;
					}

					.time-value {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						color: #333;

						.date-part {
							color: #666;
							font-family: "DIN Alternate", "Arial", sans-serif;
							background: #f5f5f5;
							padding: 4rpx 12rpx;
							border-radius: 4rpx;
						}

						.time-part {
							margin-left: 16rpx;
							padding: 4rpx 16rpx;
							border-radius: 6rpx;
							font-family: "DIN Alternate", "Arial", sans-serif;
							font-weight: 600;
							position: relative;
							min-width: 120rpx;
							text-align: center;
						}
					}
				}
			}
		}
	}

	@keyframes ripple {
		0% {
			transform: translate(-50%, -50%) scale(1);
			opacity: 0.2;
		}

		100% {
			transform: translate(-50%, -50%) scale(1.5);
			opacity: 0;
		}
	}

	@keyframes shine {
		0% {
			left: -100%;
		}

		50% {
			left: 100%;
		}

		100% {
			left: 100%;
		}
	}

	/* uView 滑动组件样式优化 */
	/deep/ .u-swipe-action-item__right {
		height: 100%;
		display: flex;
		align-items: stretch;
	}

	/deep/ .u-swipe-action-item__right__button {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 !important;
		position: relative;
		overflow: hidden;
		flex-direction: column;
		gap: 8rpx;
	}

	/* 按钮点击效果 */
	/deep/ .u-swipe-action-item__right__button:active {
		opacity: 0.8;
		transform: scale(0.98);
	}

	/* 移除默认图标 */
	/deep/ .u-swipe-action-item__right__button::before {
		content: none !important;
		display: none !important;
	}

	/* 通过按钮样式 */
	/deep/ .u-swipe-action-item__right__button:nth-child(1) {
		background-image: linear-gradient(45deg, #19be6b, #52c41a);
	}

	/* 拒绝按钮样式 */
	/deep/ .u-swipe-action-item__right__button:nth-child(2) {
		background-image: linear-gradient(45deg, #ff4d4f, #ff7875);
	}

	/* ==================== 新增违规记录样式 ==================== */

	/* 浮动按钮 */
	.add-violation-fab {
		position: fixed;
		right: 30rpx;
		bottom: 200rpx;
		width: 110rpx;
		height: 110rpx;
		background: linear-gradient(135deg, #2979ff, #1565c0);
		border-radius: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.3);
		z-index: 999;
	}

	.fab-icon {
		margin-bottom: 4rpx;
	}

	.fab-text {
		font-size: 20rpx;
		color: #ffffff;
		font-weight: 500;
	}

	/* 新增违规弹窗 */
	.add-violation-modal {
		padding: 40rpx;
		max-height: 80vh;
		overflow-y: auto;
	}

	.modal-section {
		margin-bottom: 40rpx;
	}

	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.section-title text {
		margin-left: 12rpx;
	}

	.input-group {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 20rpx;
	}

	.input-row {
		margin-bottom: 16rpx;
	}

	.owner-info {
		display: flex;
		justify-content: space-between;
		padding: 16rpx 20rpx;
		background: #e3f2fd;
		border-radius: 8rpx;
		margin-top: 16rpx;
	}

	.owner-name,
	.owner-phone {
		font-size: 28rpx;
		color: #1976d2;
	}

	/* 违规类型选择 */
	.violation-types {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 16rpx;
	}

	.type-option {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx 16rpx;
		background: #f5f5f5;
		border-radius: 12rpx;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
	}

	.type-option.selected {
		background: #e3f2fd;
		border-color: #2979ff;
	}

	.type-icon {
		font-size: 40rpx;
		margin-bottom: 8rpx;
	}

	.type-name {
		font-size: 24rpx;
		color: #666;
		text-align: center;
	}

	.type-option.selected .type-name {
		color: #2979ff;
		font-weight: 600;
	}

	.custom-input {
		margin-top: 20rpx;
	}

	/* 位置输入 */
	.location-input {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 20rpx;
	}

	/* 现场取证 */
	.evidence-section {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 20rpx;
	}

	.photo-upload {
		margin-bottom: 20rpx;
	}

	.upload-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 16rpx;
	}

	.photo-item {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.photo-item image {
		width: 100%;
		height: 100%;
	}

	.photo-delete {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 32rpx;
		height: 32rpx;
		background: rgba(0, 0, 0, 0.6);
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.photo-add {
		width: 160rpx;
		height: 160rpx;
		border: 2rpx dashed #ccc;
		border-radius: 8rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #999;
	}

	.photo-add text {
		font-size: 24rpx;
		margin-top: 8rpx;
	}

	.voice-memo {
		border-top: 1rpx solid #eee;
		padding-top: 20rpx;
	}

	.voice-player {
		display: flex;
		gap: 16rpx;
	}

	/* 操作按钮 */
	.modal-actions {
		display: flex;
		gap: 20rpx;
		margin-top: 40rpx;
	}

	.modal-actions .u-button {
		flex: 1;
	}

	/* 车牌扫描弹窗 */
	.plate-scan-modal {
		padding: 40rpx;
		text-align: center;
	}

	.scan-area {
		position: relative;
		margin: 40rpx 0;
	}

	.scan-frame {
		width: 400rpx;
		height: 200rpx;
		border: 4rpx solid #2979ff;
		border-radius: 12rpx;
		margin: 0 auto;
		position: relative;
		overflow: hidden;
	}

	.scan-line {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 4rpx;
		background: linear-gradient(90deg, transparent, #2979ff, transparent);
		animation: scan 2s linear infinite;
	}

	@keyframes scan {
		0% {
			transform: translateY(0);
		}

		100% {
			transform: translateY(196rpx);
		}
	}

	.scan-tip {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666;
	}

	.scan-result {
		margin: 40rpx 0;
		padding: 30rpx;
		background: #f0f9ff;
		border-radius: 12rpx;
	}

	.result-title {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
	}

	.result-plate {
		font-size: 36rpx;
		font-weight: 600;
		color: #2979ff;
		margin-bottom: 20rpx;
	}

	.result-actions {
		display: flex;
		gap: 16rpx;
	}

	.scan-actions {
		display: flex;
		gap: 20rpx;
	}

	.scan-actions .u-button {
		flex: 1;
	}
</style>