<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.parkingmanage.mapper.PatrolMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.parkingmanage.entity.Patrol">
        <id column="id" property="id"/>
        <result column="usercode" property="usercode"/>
        <result column="username" property="username"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="district" property="district"/>
        <result column="community" property="community"/>
        <result column="createdate" property="createdate"/>
        <result column="createman" property="createman"/>
        <result column="status" property="status"/>
        <result column="auditdate" property="auditdate"/>
        <result column="openid" property="openid"/>
        <result column="confirmdate" property="confirmdate"/>
        <result column="phone" property="phone"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, usercode, username, province, city, district, community, createdate, createman, status, auditdate, openid, confirmdate,phone
    </sql>
    <select id="duplicate" resultType="java.lang.Integer">
        select count(*) from patrol where province=#{province} and city=#{city}
        and district=#{district} and community=#{community} and usercode=#{usercode}
        <if test="id!=null and id !=''">
            and id &lt;&gt;#{id}
        </if>
    </select>
    <select id="getPatrolByOpenId" resultType="com.parkingmanage.entity.Patrol">
         select a.*
        from patrol a
        where a.openid=#{openid}
    </select>
</mapper>
