package com.parkingmanage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.parkingmanage.entity.Violations;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 违规记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface ViolationsService extends IService<Violations> {

    /**
     * 创建违规记录
     */
    boolean createViolation(Violations violation);

    /**
     * 分页查询违规记录
     */
    IPage<Map<String, Object>> getViolationsWithOwnerInfo(
            Page<Map<String, Object>> page,
            String plateNumber,
            String status,
            String violationType,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Integer ownerId
    );

    /**
     * 更新违规记录状态
     */
    boolean updateViolationStatus(Long id, String status, String remark, Integer handlerId);

    /**
     * 获取高风险车辆列表
     */
    List<Map<String, Object>> getHighRiskVehicles(LocalDateTime startDate, LocalDateTime endDate, Integer limit);

    /**
     * 获取违规统计数据
     */
    Map<String, Object> getViolationStatistics(LocalDateTime startDate, LocalDateTime endDate, String plateNumber);

    /**
     * 根据车牌号查询车主信息
     */
    Map<String, Object> getOwnerByPlateNumber(String plateNumber);

    /**
     * 获取车牌号搜索建议
     */
    List<Map<String, Object>> getPlateSuggestions(String keyword);

    /**
     * 获取车主的车辆列表
     */
    List<Map<String, Object>> getOwnerVehicles(Integer ownerId);

    /**
     * 更新车主信用分
     */
    boolean updateOwnerCreditScore(Integer ownerId, Integer creditScore);
}
