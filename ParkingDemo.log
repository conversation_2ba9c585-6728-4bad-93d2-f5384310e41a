2025-08-01 08:52:59.405  INFO 31176 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 31176 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 08:52:59.407  INFO 31176 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 08:52:59.433  INFO 31176 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 08:52:59.433  INFO 31176 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 08:53:00.384  INFO 31176 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port(s): 8543 (http)
2025-08-01 08:53:00.389  INFO 31176 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 08:53:00.390  INFO 31176 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 08:53:00.440  INFO 31176 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 08:53:00.440  INFO 31176 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1006 ms
2025-08-01 08:53:00.811  WARN 31176 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 08:53:01.848  INFO 31176 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 08:53:01.873  INFO 31176 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 08:53:01.911  INFO 31176 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 08:53:02.072  INFO 31176 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 08:53:02.073  INFO 31176 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 08:53:02.083  INFO 31176 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 08:53:02.116  INFO 31176 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 08:53:02.220  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 08:53:02.222  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 08:53:02.230  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 08:53:02.232  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 08:53:02.236  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 08:53:02.239  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 08:53:02.250  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 08:53:02.251  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 08:53:02.252  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 08:53:02.253  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 08:53:02.254  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 08:53:02.255  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 08:53:02.256  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 08:53:02.256  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 08:53:02.257  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 08:53:02.258  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 08:53:02.260  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 08:53:02.261  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 08:53:02.262  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 08:53:02.263  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 08:53:02.265  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 08:53:02.267  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 08:53:02.267  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 08:53:02.272  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 08:53:02.272  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 08:53:02.274  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 08:53:02.276  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 08:53:02.278  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 08:53:02.278  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 08:53:02.280  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 08:53:02.282  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 08:53:02.283  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 08:53:02.283  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 08:53:02.285  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 08:53:02.286  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 08:53:02.288  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 08:53:02.292  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 08:53:02.292  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 08:53:02.295  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 08:53:02.297  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 08:53:02.300  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 08:53:02.301  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 08:53:02.301  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 08:53:02.301  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 08:53:02.305  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 08:53:02.307  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 08:53:02.308  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 08:53:02.308  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 08:53:02.309  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 08:53:02.309  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 08:53:02.309  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 08:53:02.310  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 08:53:02.310  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 08:53:02.312  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 08:53:02.312  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 08:53:02.320  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 08:53:02.321  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 08:53:02.321  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 08:53:02.323  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 08:53:02.323  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 08:53:02.326  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 08:53:02.327  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 08:53:02.332  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 08:53:02.333  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 08:53:02.334  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 08:53:02.334  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 08:53:02.334  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 08:53:02.335  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 08:53:02.335  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 08:53:02.336  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 08:53:02.338  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 08:53:02.339  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 08:53:02.339  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 08:53:02.340  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 08:53:02.342  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 08:53:02.343  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 08:53:02.344  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 08:53:02.345  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 08:53:02.348  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 08:53:02.350  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 08:53:02.350  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 08:53:02.351  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 08:53:02.351  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 08:53:02.352  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 08:53:02.354  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 08:53:02.355  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 08:53:02.356  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 08:53:02.356  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 08:53:02.357  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 08:53:02.357  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 08:53:02.358  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 08:53:02.358  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 08:53:02.359  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 08:53:02.359  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 08:53:02.359  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 08:53:02.360  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 08:53:02.360  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 08:53:02.360  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 08:53:02.361  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 08:53:02.361  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 08:53:02.362  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 08:53:02.362  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 08:53:02.365  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 08:53:02.366  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 08:53:02.367  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 08:53:02.368  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 08:53:02.369  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 08:53:02.370  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 08:53:02.372  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 08:53:02.373  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 08:53:02.374  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 08:53:02.375  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 08:53:02.376  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 08:53:02.376  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 08:53:02.376  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 08:53:02.380  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 08:53:02.381  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 08:53:02.381  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 08:53:02.383  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 08:53:02.384  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 08:53:02.385  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 08:53:02.388  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 08:53:02.391  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 08:53:02.406  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 08:53:02.419  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 08:53:02.420  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 08:53:02.421  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 08:53:02.421  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 08:53:02.422  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 08:53:02.422  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 08:53:02.422  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 08:53:02.429  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 08:53:02.431  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 08:53:02.432  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 08:53:02.447  INFO 31176 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 3.259 seconds (JVM running for 3.78)
2025-08-01 08:53:02.528  INFO 31176 --- [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 08:53:02.528  INFO 31176 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 08:53:02.529  INFO 31176 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 08:53:02.890  INFO 31176 --- [RMI TCP Connection(4)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-01 08:54:32.696  WARN 31176 --- [http-nio-8543-exec-6] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 79213
2025-08-01 09:01:50.405  WARN 31176 --- [http-nio-8543-exec-10] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 437694
2025-08-01 09:04:17.426  WARN 31176 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 117829
2025-08-01 09:07:27.242  WARN 31176 --- [http-nio-8543-exec-6] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 135925
2025-08-01 09:08:00.515  INFO 31176 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 09:08:00.523  INFO 31176 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-08-01 09:49:42.628  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 12172 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 09:49:42.629  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 09:49:42.651  INFO 12172 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 09:49:42.651  INFO 12172 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 09:49:44.292  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-08-01 09:49:44.296  INFO 12172 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 09:49:44.296  INFO 12172 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 09:49:44.341  INFO 12172 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 09:49:44.341  INFO 12172 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1690 ms
2025-08-01 09:49:44.820  WARN 12172 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 09:49:45.828  INFO 12172 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 09:49:45.852  INFO 12172 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 09:49:45.888  INFO 12172 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 09:49:46.059  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 09:49:46.060  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 09:49:46.066  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 09:49:46.095  INFO 12172 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 09:49:46.197  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 09:49:46.200  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 09:49:46.208  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 09:49:46.209  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 09:49:46.213  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 09:49:46.216  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 09:49:46.227  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 09:49:46.228  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 09:49:46.229  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 09:49:46.229  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 09:49:46.231  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 09:49:46.232  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 09:49:46.232  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 09:49:46.233  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 09:49:46.234  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 09:49:46.235  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 09:49:46.236  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 09:49:46.236  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 09:49:46.238  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 09:49:46.238  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 09:49:46.240  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 09:49:46.242  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 09:49:46.243  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 09:49:46.247  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 09:49:46.247  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 09:49:46.249  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 09:49:46.251  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 09:49:46.253  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 09:49:46.253  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 09:49:46.254  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 09:49:46.255  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 09:49:46.256  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 09:49:46.257  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 09:49:46.258  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 09:49:46.260  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 09:49:46.261  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 09:49:46.264  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 09:49:46.264  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 09:49:46.266  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 09:49:46.267  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 09:49:46.270  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 09:49:46.270  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 09:49:46.271  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 09:49:46.271  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 09:49:46.275  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 09:49:46.277  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 09:49:46.278  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 09:49:46.278  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 09:49:46.279  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 09:49:46.279  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 09:49:46.279  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 09:49:46.280  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 09:49:46.281  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 09:49:46.282  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 09:49:46.282  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 09:49:46.290  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 09:49:46.291  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 09:49:46.291  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 09:49:46.292  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 09:49:46.293  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 09:49:46.296  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 09:49:46.297  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 09:49:46.303  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 09:49:46.303  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 09:49:46.304  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 09:49:46.305  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 09:49:46.305  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 09:49:46.305  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 09:49:46.306  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 09:49:46.307  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 09:49:46.308  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 09:49:46.309  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 09:49:46.309  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 09:49:46.310  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 09:49:46.311  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 09:49:46.312  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 09:49:46.313  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 09:49:46.314  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 09:49:46.317  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 09:49:46.320  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 09:49:46.320  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 09:49:46.321  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 09:49:46.322  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 09:49:46.323  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 09:49:46.324  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 09:49:46.325  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 09:49:46.326  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 09:49:46.327  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 09:49:46.327  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 09:49:46.328  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 09:49:46.328  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 09:49:46.328  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 09:49:46.329  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 09:49:46.329  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 09:49:46.329  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 09:49:46.330  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 09:49:46.330  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 09:49:46.330  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 09:49:46.331  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 09:49:46.331  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 09:49:46.332  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 09:49:46.332  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 09:49:46.334  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 09:49:46.336  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 09:49:46.336  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 09:49:46.338  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 09:49:46.339  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 09:49:46.339  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 09:49:46.341  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 09:49:46.342  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 09:49:46.342  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 09:49:46.343  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 09:49:46.344  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 09:49:46.344  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 09:49:46.345  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 09:49:46.348  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 09:49:46.349  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 09:49:46.349  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 09:49:46.351  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 09:49:46.352  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 09:49:46.353  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 09:49:46.357  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 09:49:46.360  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 09:49:46.375  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 09:49:46.388  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 09:49:46.389  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 09:49:46.389  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 09:49:46.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 09:49:46.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 09:49:46.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 09:49:46.391  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 09:49:46.396  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 09:49:46.397  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 09:49:46.398  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 09:49:46.411  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 3.97 seconds (JVM running for 4.504)
2025-08-01 09:49:46.670  INFO 12172 --- [RMI TCP Connection(1)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-01 09:49:47.075  INFO 12172 --- [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 09:49:47.075  INFO 12172 --- [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 09:49:47.076  INFO 12172 --- [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 10:54:53.945  INFO 12172 --- [Thread-11] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 10:54:53.950  INFO 12172 --- [Thread-11] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
