package com.parkingmanage.entity;

import lombok.Data;

import java.util.List;

/**
 * @program: ParkManage
 * @description:
 * @author: lzx
 * @create: 2024-04-26 08:50
 **/
@Data
public class ReportCarOutData {
    //    private String leaveChannelCode;
//    private int chargeState;
//    private String enterStatisticalArea;
//    private String inOperatorName;
//    private int discountAmount;
//    private int leaveChannelId;
//    private String outOperatorName;
//    private int leaveType;
//    private int leaveCarLicenseColor;
//    private int leaveRecognitionConfidence;
//    private String lockKey;
//    private String enterTime;
//    private int enterNovipCode;
//    private String leaveTime;
//    private int enterCarType;
//    private String leaveChannelName;
//    private List<LeaveImageArray> leaveImageArray;
//    private ExtendInfoOut extendInfo;
//    private String parkingCode;
//    private int totalAmount;
//    private int enterRecognitionConfidence;
//    private String cmd;
//    private String parkCode;
//    private int enterCarLicenseType;
//    private int enterCarLogo;
//    private int isCorrect;
//    private int enterVipType;
//    private String enterChannelCode;
//    private int isParkInOut;
//    private String inOperatorTime;
//    private String enterCarLicenseNumber;
//    private String enterCustomVipName;
//    private int enterChannelId;
//    private String outOperatorTime;
//    private long stoppingTime;
//    private int enterCarLicenseColor;
//    private int enterCarColor;
//    private String leaveCarLicenseNumber;
//    private int leaveNovipCode;
//    private String leaveChannelCustomCode;
//    private int actualReceivable;
//    private String enterChannelCustomCode;
//    private List<EnterImageArray> enterImageArray;
//    private int confidence;
//    private int amountReceivable;
//    private String enterChannelName;
//    private String carLicenseNumber;
//    private int enterType;
//    private int leaveCarType;
//    private String enterCarCardNumber;
//    private String leaveCarCardNumber;
//    private int areaId;
//    private int leaveVipType;
//    private String leaveChannelCode;
//    private int chargeState;
//    private String enterStatisticalArea;
//    private String inOperatorName;
//    private int discountAmount;
//    private int leaveChannelId;
//    private String outOperatorName;
//    private int leaveType;
//    private int leaveCarLicenseColor;
//    private int leaveRecognitionConfidence;
//    private String lockKey;
//    private String enterTime;
//    private int enterNovipCode;
//    private String leaveTime;
//    private int enterCarType;
//    private String leaveChannelName;
//    private List<LeaveImageArray> leaveImageArray;
//    private ExtendInfoOut extendInfo;
//    private String parkingCode;
//    private int totalAmount;
//    private int enterRecognitionConfidence;
//    private String cmd;
//    private String parkCode;
//    private int enterCarLicenseType;
//    private int enterCarLogo;
//    private int isCorrect;
//    private int enterVipType;
//    private String enterChannelCode;
//    private int isParkInOut;
//    private String inOperatorTime;
//    private String enterCarLicenseNumber;
//    private String enterCustomVipName;
//    private int enterChannelId;
//    private String outOperatorTime;
//    private long stoppingTime;
//    private int enterCarLicenseColor;
//    private int enterCarColor;
//    private long enterVipTicketId;
//    private String leaveCarLicenseNumber;
//    private int leaveNovipCode;
//    private String leaveChannelCustomCode;
//    private int actualReceivable;
//    private String enterChannelCustomCode;
//    private List<EnterImageArray> enterImageArray;
//    private int confidence;
//    private int amountReceivable;
//    private String enterChannelName;
//    private String carLicenseNumber;
//    private int enterType;
//    private int leaveCarType;
//    private String enterCarCardNumber;
//    private String leaveCarCardNumber;
//    private long areaId;
//    private int enterVipTypeId;
//    private int leaveVipType;

    private String leaveChannelCode;
    private int chargeState;
    private String enterStatisticalArea;
    private String inOperatorName;
    private int discountAmount;
    private long leaveChannelId;
    private String outOperatorName;
    private int leaveType;
    private String fromAreaId;
    private int leaveCarLicenseColor;
    private int leaveRecognitionConfidence;
    private String lockKey;
    private String enterTime;
    private int enterNovipCode;
    private String leaveTime;
    private int enterCarType;
    private String leaveChannelName;
    private List<LeaveImageArray> leaveImageArray;
    private ExtendInfoOut extendInfo;
    private String parkingCode;
    private int totalAmount;
    private int enterRecognitionConfidence;
    private String cmd;
    private String parkCode;
    private int enterCarLicenseType;
    private int enterCarLogo;
    private int isCorrect;
    private int enterVipType;
    private String enterChannelCode;
    private int isParkInOut;
    private String inOperatorTime;
    private String enterCarLicenseNumber;
    private String enterCustomVipName;
    private int enterChannelId;
    private String outOperatorTime;
    private int stoppingTime;
    private int enterCarLicenseColor;
    private int enterCarColor;
    private long enterVipTicketId;
    private String leaveCarLicenseNumber;
    private int leaveNovipCode;
    private String leaveChannelCustomCode;
    private int actualReceivable;
    private String toAreaId;
    private String enterChannelCustomCode;
    private List<EnterImageArray> enterImageArray;
    private int confidence;
    private int amountReceivable;
    private String enterChannelName;
    private String carLicenseNumber;
    private int enterType;
    private int leaveCarType;
    private String enterCarCardNumber;
    private String leaveCarCardNumber;
    private int areaId;
    private int enterVipTypeId;
    private int leaveVipType;
}
