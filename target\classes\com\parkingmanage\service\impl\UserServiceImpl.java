package com.parkingmanage.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.parkingmanage.common.exception.ServiceException;
import com.parkingmanage.entity.User;
import com.parkingmanage.mapper.UserMapper;
import com.parkingmanage.service.UserService;
import com.parkingmanage.utils.AesUtil;
import com.parkingmanage.utils.TokenUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 <p>
 服务实现类
 </p>

 <AUTHOR>
 @since 2022-02-27
*/
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Resource
    private HttpServletRequest request;
    @Autowired UserMapper userMapper;
    @Override
    public User login(String loginName, String password) {
        User user = userMapper.selectByUserName(loginName);
        if (user == null) {
                throw new ServiceException("用户名不存在或用户名输入错误");
        }
        if (!AesUtil.encrypt(password).equals(user.getPassword())) {
            throw new ServiceException("用户名或密码输入错误");
        }
        String token = TokenUtils.genToken(user.getUserId().toString(), user.getPassword());
        user.setToken(token);
        //TODO: 2023/1/31 登录失败信息返回前端
//        User user = this.getOne(Wrappers.<User>lambdaQuery().eq(User::getLoginName, loginName).eq(User::getPassword, AesUtil.encrypt(password)));
//        if (!ObjectUtils.isEmpty(user)) {
//            //登录成功信息存到后端缓存
//            request.getSession().setAttribute("user", user);
//            return user;
//        } else {
//            return null;
//        }
        return user;
    }
}
