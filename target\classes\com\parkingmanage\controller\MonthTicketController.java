package com.parkingmanage.controller;


import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.parkingmanage.common.HttpClientUtil;
import com.parkingmanage.common.Result;
import com.parkingmanage.common.config.AIKEConfig;
import com.parkingmanage.entity.*;
import com.parkingmanage.service.BlackListService;
import com.parkingmanage.service.MonthTicketService;
import com.parkingmanage.service.YardInfoService;
import com.parkingmanage.utils.PageUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@RestController
@RequestMapping("/parking/monthTicket")
public class MonthTicketController {

    @Autowired
    public AIKEConfig aikeConfig;

    @Resource
    private MonthTicketService monthTicketService;
    ;

    /**
     * 查询月票列表
     *
     * @param parkCodeList
     * @return
     */
    @ApiOperation("查询月票列表")
    @PostMapping("/getOnlineMonthTicketList")
    public ResponseEntity getOnlineMonthTicketList(String parkCodeList, String pageNum, String pageSize,String validStatus) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
//        System.out.println("parkCodeList = " + parkCodeList);
        params.put("parkCodeList", Arrays.asList(parkCodeList));
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("validStatus", Integer.parseInt(validStatus));
//        System.out.println("params = " + params);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getOnlineMonthTicketList", params);
        return ResponseEntity.ok(data);
    }

    @ApiOperation("分页查询")
    @GetMapping("/page")
    public IPage<MonthTick> findPages(@RequestParam(required = false) String parkName,
                                      @RequestParam(required = false) String carNo,
                                      @RequestParam(required = false) String ticketName,
                                      @RequestParam(required = false) String userName,
                                      @RequestParam(required = false) String timePeriodList,
                                      @RequestParam(required = false) String userPhone,
                                      @RequestParam(required = false) Integer timeDays,
                                      @RequestParam(required = false) String remark1, @RequestParam(required = false) String remark2,
                                      @RequestParam(required = false) String remark3,
                                      @RequestParam(required = false) Integer isValid,
                                      @RequestParam(required = false) Integer isFrozen,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                      @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        // TODO 编写查询逻辑
        List<MonthTick> monthTickPage = monthTicketService.queryInfoOnly(parkName, carNo, ticketName, userName, timeDays, timePeriodList, userPhone, remark1, remark2, remark3, isFrozen, isValid);
        List<MonthTick> asServices = monthTickPage.stream().sorted(Comparator.comparing(MonthTick::getCarNo)).collect(Collectors.toList());
        return PageUtils.getPage(asServices, pageNum, pageSize);
    }

    /**
     * 查询车场开通的月票名称
     *
     * @param parkCodeList
     * @return
     */
    @ApiOperation("查询月票列表")
    @GetMapping("/getMonthTicketConfigDetailList")
    public ResponseEntity getMonthTicketConfigDetailList(String parkCodeList) {
        HashMap<String, Object> params = new HashMap<>();
//        System.out.println("parkCodeList = " + parkCodeList);
        params.put("parkCodeList", Arrays.asList(parkCodeList));
        params.put("pageNum", 1);
        params.put("pageSize", 100);
//        System.out.println("params = " + params);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "getMonthTicketConfigDetailList", params);
        return ResponseEntity.ok(data);
    }

    /**
     * 调用接口查询更新数据
     *
     * @param parkName
     * @return
     */
    @ApiOperation("分页查询")
    @GetMapping("/AKEPage")
    public ResponseEntity findPage(@RequestParam(required = false) String parkName,@RequestParam(required = false) String validStatus) {
        // validStatus：1(生效)、4(过期)
        // 调用接口查询车场名称
        HashMap<String, String> hashMap = new HashMap<>();
        ArrayList<String> parkCodeLists = new ArrayList<>();
        parkCodeLists.add("2KST9MNP");
        parkCodeLists.add("2KUG6XLU");
        if (parkName.equals("万象上东")) {
            hashMap.put("parkCodeList", "2KST9MNP");
        } else if (parkName.equals("四季上东")) {
            hashMap.put("parkCodeList", "2KUG6XLU");
        }
        hashMap.put("pageSize", "100");
        hashMap.put("validStatus", validStatus);
//        System.out.println("hashMap = " + hashMap);
//        String get = HttpClientUtil.doGet("https://www.xuerparking.cn:8543/parking/monthTicket/getOnlineMonthTicketList", hashMap);
        String get = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/monthTicket/getOnlineMonthTicketList", hashMap);
        JSONObject jsonObject = JSONObject.parseObject(get);
        System.out.println("jsonObject = " + jsonObject);
        // 将查询到的data中的total进行计算还需要轮询多少次
        JSONObject data1 = (JSONObject) jsonObject.get("data");
        JSONObject data2 = (JSONObject) data1.get("data");
        Integer total = data2.getInteger("total");
//        System.out.println("total = " + total);
        JSONArray recordList = data2.getJSONArray("recordList");
        ArrayList<MonthTick> monthTicks = new ArrayList<>();
        for (int i = 0; i < recordList.size(); i++) {
            JSONObject jsonObject1 = recordList.getJSONObject(i);
            // 存储到集合中
            MonthTick monthTick = new MonthTick();
            monthTick.setCarNo(jsonObject1.getString("carNo"));
            monthTick.setCreateTime(jsonObject1.getString("createTime"));
            monthTick.setCreateBy(jsonObject1.getString("createBy"));
            monthTick.setTicketName(jsonObject1.getString("ticketName"));
            if (jsonObject1.getInteger("carNoNum") == null) {
                monthTick.setCarNoNum(-1);
            } else {
                monthTick.setCarNoNum(jsonObject1.getInteger("carNoNum"));
            }
            if (jsonObject1.getString("userPhone") == null) {
                monthTick.setUserPhone("11111111111");
            } else {
                monthTick.setUserPhone(jsonObject1.getString("userPhone"));
            }
            monthTick.setRemark1(jsonObject1.getString("remark1"));
            monthTick.setRemark2(jsonObject1.getString("remark2"));
            monthTick.setRemark3(jsonObject1.getString("remark3"));
            monthTick.setIsFrozen(jsonObject1.getInteger(("isFrozen")));
            monthTick.setUserName(jsonObject1.getString("userName"));
            monthTick.setUserName(jsonObject1.getString("userName"));
            monthTick.setValidStatus(jsonObject1.getInteger("validStatus"));
            JSONArray timePeriodList1 = jsonObject1.getJSONArray("timePeriodList");
            ArrayList<TimePeriodList> timePeriodLists = new ArrayList<>();
            for (int i2 = 0; i2 < timePeriodList1.size(); i2++) {
                JSONObject jsonObjectTime = timePeriodList1.getJSONObject(i2);
                TimePeriodList timePeriodListParams = new TimePeriodList();
                timePeriodListParams.setStartTime(convertDateFormat(jsonObjectTime.getString("startTime")));
                timePeriodListParams.setEndTime(convertDateFormat(jsonObjectTime.getString("endTime")));
//                System.out.println("timePeriodListParams = " + timePeriodListParams);
                timePeriodLists.add(timePeriodListParams);
            }
            // 将timePeriodLists中的对象转为字符串
            StringBuilder str = new StringBuilder();
            for (int j = 0; j < timePeriodLists.size(); j++) {
                str.append(timePeriodLists.get(j).toString());
                if (j < timePeriodLists.size() - 1) {
                    str.append(",");
                }
            }
            monthTick.setTimePeriodList(str.toString());
            // 添加车场名称
            if (parkName.equals("万象上东")) {
                monthTick.setParkName("万象上东");
            } else if (parkName.equals("四季上东")) {
                monthTick.setParkName("四季上东");
            }
            monthTicks.add(monthTick);
        }

        // 计算还需要轮询的次数
        int n = total / 100;
        int remainder = total % 100;
        if (remainder != 0) {
            // 还需要的轮询次数
            for (int i = 2; i <= (n + 1); i++) {
                HashMap<String, String> hashMapOut = new HashMap<>();
                if (parkName.equals("万象上东")) {
                    hashMapOut.put("parkCodeList", "2KST9MNP");
                } else if (parkName.equals("四季上东")) {
                    hashMapOut.put("parkCodeList", "2KUG6XLU");
                }
                hashMapOut.put("pageNum", String.valueOf(i));
                hashMapOut.put("pageSize", "100");
                hashMapOut.put("validStatus", validStatus);
//                String getIn = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/monthTicket/getOnlineMonthTicketList", hashMapOut);
                String getIn = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/monthTicket/getOnlineMonthTicketList", hashMapOut);
                JSONObject jsonObjectIn = JSONObject.parseObject(getIn);
                JSONObject dataInfo = (JSONObject) jsonObjectIn.get("data");
                JSONObject dataInfo1 = (JSONObject) dataInfo.get("data");
                JSONArray recordOutList = (JSONArray) dataInfo1.get("recordList");
                for (int i1 = 0; i1 < recordOutList.size(); i1++) {
                    JSONObject jsonObject1 = recordOutList.getJSONObject(i1);
                    // 存储到集合中
                    MonthTick monthTick = new MonthTick();
                    monthTick.setCarNo(jsonObject1.getString("carNo"));
                    monthTick.setCreateTime(jsonObject1.getString("createTime"));
                    monthTick.setCreateBy(jsonObject1.getString("createBy"));
                    monthTick.setTicketName(jsonObject1.getString("ticketName"));
                    if (jsonObject1.getInteger("carNoNum") == null) {
                        monthTick.setCarNoNum(-1);
                    } else {
                        monthTick.setCarNoNum(jsonObject1.getInteger("carNoNum"));
                    }
                    monthTick.setUserPhone(jsonObject1.getString("userPhone"));
                    monthTick.setRemark1(jsonObject1.getString("remark1"));
                    monthTick.setRemark2(jsonObject1.getString("remark2"));
                    monthTick.setRemark3(jsonObject1.getString("remark3"));
                    monthTick.setIsFrozen(jsonObject1.getInteger(("isFrozen")));
                    monthTick.setUserName(jsonObject1.getString("userName"));
                    monthTick.setUserName(jsonObject1.getString("userName"));
                    monthTick.setValidStatus(jsonObject1.getInteger("validStatus"));
                    JSONArray timePeriodList1 = jsonObject1.getJSONArray("timePeriodList");
                    ArrayList<TimePeriodList> timePeriodListIn = new ArrayList<>();
                    for (int i2 = 0; i2 < timePeriodList1.size(); i2++) {
                        JSONObject jsonObjectInData = timePeriodList1.getJSONObject(i2);
                        TimePeriodList timePeriodListInData = new TimePeriodList();
                        timePeriodListInData.setStartTime(convertDateFormat(jsonObjectInData.getString("startTime")));
                        timePeriodListInData.setEndTime(convertDateFormat(jsonObjectInData.getString("endTime")));
                        timePeriodListIn.add(timePeriodListInData);
//                        System.out.println("timePeriodListParams = " + timePeriodListInData);
                    }
                    StringBuilder strIn = new StringBuilder();
                    for (int j = 0; j < timePeriodListIn.size(); j++) {
                        strIn.append(timePeriodListIn.get(j).toString());
                        if (j < timePeriodListIn.size() - 1) {
                            strIn.append(",");
                        }
                    }
//                    System.out.println("strIn = " + strIn);
                    monthTick.setTimePeriodList(strIn.toString());
                    // 添加车场名称
                    if (parkName.equals("万象上东")) {
                        monthTick.setParkName("万象上东");
                    } else if (parkName.equals("四季上东")) {
                        monthTick.setParkName("四季上东");
                    }
                    monthTicks.add(monthTick);
                }
            }
        } else {
            for (int i = 2; i <= n; i++) {
                HashMap<String, String> hashMapElse = new HashMap<>();
                if (parkName.equals("万象上东")) {
                    hashMapElse.put("parkCodeList", "2KST9MNP");
                } else if (parkName.equals("四季上东")) {
                    hashMapElse.put("parkCodeList", "2KUG6XLU");
                }
                hashMapElse.put("pageNum", String.valueOf(i));
                hashMapElse.put("pageSize", "100");
                hashMapElse.put("validStatus", validStatus);
//                String getElse = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/monthTicket/getOnlineMonthTicketList", hashMapElse);
                String getElse = HttpClientUtil.doPost("https://www.xuerparking.cn:8543/parking/monthTicket/getOnlineMonthTicketList", hashMapElse);
                JSONObject jsonObjectElse = JSONObject.parseObject(getElse);
                JSONObject dataInfoElse = (JSONObject) jsonObjectElse.get("data");
                JSONObject dataInfoElse1 = (JSONObject) dataInfoElse.get("data");
                JSONArray recordOutList = (JSONArray) dataInfoElse1.get("recordList");
                for (int i1 = 0; i1 < recordOutList.size(); i1++) {
                    JSONObject jsonObject1 = recordOutList.getJSONObject(i1);
                    // 存储到集合中
                    MonthTick monthTick = new MonthTick();
                    monthTick.setCarNo(jsonObject1.getString("carNo"));
                    monthTick.setCreateTime(jsonObject1.getString("createTime"));
                    monthTick.setCreateBy(jsonObject1.getString("createBy"));
                    monthTick.setTicketName(jsonObject1.getString("ticketName"));
                    monthTick.setCarNoNum(jsonObject1.getInteger("carNoNum"));
                    monthTick.setRemark1(jsonObject1.getString("remark1"));
                    monthTick.setRemark2(jsonObject1.getString("remark2"));
                    monthTick.setRemark3(jsonObject1.getString("remark3"));
                    monthTick.setUserPhone(jsonObject1.getString("userPhone"));
                    monthTick.setIsFrozen(jsonObject1.getInteger(("isFrozen")));
                    monthTick.setUserName(jsonObject1.getString("userName"));
                    monthTick.setUserName(jsonObject1.getString("userName"));
                    monthTick.setValidStatus(jsonObject1.getInteger("validStatus"));
                    JSONArray timePeriodList1 = jsonObject1.getJSONArray("timePeriodList");
                    ArrayList<TimePeriodList> timePeriodLists1 = new ArrayList<>();
                    for (int i2 = 0; i2 < timePeriodList1.size(); i2++) {
                        JSONObject jsonObjectELse = timePeriodList1.getJSONObject(i2);
                        TimePeriodList timePeriodListELse = new TimePeriodList();
                        timePeriodListELse.setStartTime(convertDateFormat(jsonObjectELse.getString("startTime")));
                        timePeriodListELse.setEndTime(convertDateFormat(jsonObjectELse.getString("endTime")));
                        timePeriodLists1.add(timePeriodListELse);
//                        System.out.println("timePeriodListELse = " + timePeriodListELse);
                    }
                    StringBuilder strData = new StringBuilder();
                    for (int j = 0; j < timePeriodLists1.size(); j++) {
                        strData.append(timePeriodLists1.get(j).toString());
                        if (j < timePeriodLists1.size() - 1) {
                            strData.append(",");
                        }
                    }
//                    System.out.println("strData = " + strData);
//                    System.out.println("timePeriodListsElse = " + timePeriodLists1);
                    monthTick.setTimePeriodList(strData.toString());
                    // 添加车场名称
                    if (parkName.equals("万象上东")) {
                        monthTick.setParkName("万象上东");
                    } else if (parkName.equals("四季上东")) {
                        monthTick.setParkName("四季上东");
                    }
                    monthTicks.add(monthTick);
                }
            }
        }
        int updateNum = 0;
        int InsertNum = 0;
        // 将这个列表批量添加进数据库中
        for (MonthTick monthTick : monthTicks) {
            if (monthTicketService.findOne(monthTick).isEmpty()) {
                boolean save = monthTicketService.save(monthTick);
                if (save) {
                    InsertNum++;
                }
            } else {
                boolean b = monthTicketService.updateById(monthTick);
                if (b) {
                    updateNum++;
                }
            }
        }
        Result result = new Result();
        result.setMsg("数据调用成功！");
        result.setCode("0");
        result.setData(InsertNum);
        return ResponseEntity.ok(result);
    }

    /**
     * 日期转换
     *
     * @param input
     * @return
     */
    public static String convertDateFormat(String input) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = inputFormat.parse(input);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return outputFormat.format(date);
    }

    /**
     * 格式化日日期
     *
     * @param input
     * @return
     * @throws ParseException
     * @throws IllegalArgumentException
     */
    public static String formatDateRange(String input) throws ParseException, IllegalArgumentException {
        // 去除输入字符串中的不必要空格
        input = input.replaceAll("[\\s:-]+", "");
//        System.out.println("input = " + input);
        // 在第15到16字符中间添加"-"
        String result = input.substring(0, 14) + "-" + input.substring(14);
        return result;
    }

    /**
     * 移除黑名单
     * @param parkCode
     * @param carNo
     * @return
     */
    @ApiOperation("移除黑名单")
    @GetMapping("/removeBlackListCar")
    public ResponseEntity removeBlackListCar(String parkCode, String carNo) {
        HashMap<String, Object> params = new HashMap<>();
        //enterTime格式必须是yyyy-MM-dd HH:mm:ss
        params.put("parkCode", parkCode);
        params.put("carNo", carNo);
//        System.out.println("params = " + params);
        JSONObject data = aikeConfig.downHandler(AIKEConfig.AK_URL, AIKEConfig.AK_KEY, AIKEConfig.AK_SECRET, "removeBlackListCar", params);
        return ResponseEntity.ok(data);
    }
}

