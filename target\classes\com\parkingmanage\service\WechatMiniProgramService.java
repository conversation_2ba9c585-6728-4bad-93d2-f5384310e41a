package com.parkingmanage.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.parkingmanage.config.WechatMiniAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WechatMiniProgramService {
    
    @Autowired
    private WechatMiniAppConfig wechatConfig;
    
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 检查微信配置是否有效
     */
    private boolean isWechatConfigValid() {
        String appId = wechatConfig.getAppId();
        String secret = wechatConfig.getSecret();

        // 检查配置是否为空或包含无效值
        if (appId == null || secret == null ||
            appId.trim().isEmpty() || secret.trim().isEmpty() ||
            secret.contains("INVALID") || secret.contains("DISABLE")) {
            return false;
        }

        return true;
    }
    
    /**
     * 生成访客邀请小程序码
     * @param butlerPhone 管家手机号
     * @param community 小区名称
     * @param province 省份
     * @param city 城市
     * @param district 区县
     * @return Base64编码的小程序码图片
     */
    public String generateVisitorInviteCode(String butlerPhone, String community,
                                           String province, String city, String district) {
        // 🔧 检查微信配置是否有效
        if (!isWechatConfigValid()) {
            log.warn("微信配置无效，返回错误占位符");
            return generateErrorPlaceholder("微信配置无效", "请配置正确的AppID和AppSecret");
        }

        return generateVisitorInviteCode(butlerPhone, community, province, city, district, "pages/auth/visitor-apply");
    }
    
    /**
     * 生成访客邀请小程序码（支持自定义目标页面）
     * @param butlerPhone 管家手机号
     * @param community 小区名称
     * @param province 省份
     * @param city 城市
     * @param district 区县
     * @param targetPage 目标页面路径
     * @return Base64编码的小程序码图片
     */
    public String generateVisitorInviteCode(String butlerPhone, String community, 
                                           String province, String city, String district, String targetPage) {
        log.info("=== 开始生成微信小程序码 ===");
        log.info("参数信息: 管家={}, 小区={}, 目标页面={}", butlerPhone, community, targetPage);
        log.info("微信配置检查: AppID={}, Secret长度={}", 
                wechatConfig.getAppId(), 
                wechatConfig.getSecret() != null ? wechatConfig.getSecret().length() : 0);
        
        // 1. 构建场景值（最多32位字符）
        String scene = buildSceneValue(butlerPhone, community, province, city, district);
        log.info("构建的场景值: {} (长度: {})", scene, scene.length());
        
        // 2. 验证页面路径
        if (!isValidMiniProgramPage(targetPage)) {
            log.error("无效的小程序页面路径: {}", targetPage);
            return generateErrorPlaceholder("页面路径无效", targetPage);
        }
        
        try {
            // 3. 获取Access Token
            log.info("正在获取微信Access Token...");
            String accessToken = getAccessToken();
            if (accessToken == null) {
                log.error("无法获取微信Access Token");
                return generateErrorPlaceholder("微信授权失败", "无法获取访问令牌");
            }
            log.info("微信Access Token获取成功");
            
            // 4. 尝试使用getwxacodeunlimit接口生成小程序码
            return generateWithUnlimitedCode(accessToken, scene, targetPage);
            
        } catch (Exception e) {
            log.error("生成微信小程序码失败", e);
            return handleMiniProgramCodeFailure(scene, targetPage, e);
        }
    }
    
    /**
     * 使用getwxacodeunlimit接口生成小程序码
     */
    private String generateWithUnlimitedCode(String accessToken, String scene, String targetPage) throws Exception {
        String apiUrl = String.format("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s", accessToken);
        
        // 构建请求参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("scene", scene);
        requestBody.put("page", targetPage);
        requestBody.put("width", 430);
        requestBody.put("auto_color", false);
        requestBody.put("is_hyaline", false);

        Map<String, Object> lineColor = new HashMap<>();
        lineColor.put("r", 0);
        lineColor.put("g", 0);
        lineColor.put("b", 0);
        requestBody.put("line_color", lineColor);
        
        log.info("微信API请求URL: {}", apiUrl);
        log.info("微信API请求参数: {}", requestBody);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        // 发送请求
        byte[] qrCodeBytes = restTemplate.postForObject(apiUrl, request, byte[].class);
        
        if (qrCodeBytes == null || qrCodeBytes.length == 0) {
            throw new RuntimeException("微信API返回空数据");
        }
        
        // 检查是否是错误响应
        String responseStr = new String(qrCodeBytes, 0, Math.min(200, qrCodeBytes.length));
        if (responseStr.startsWith("{")) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode errorNode = objectMapper.readTree(responseStr);
            if (errorNode.has("errcode")) {
                int errCode = errorNode.get("errcode").asInt();
                String errMsg = errorNode.get("errmsg").asText();
                log.error("微信API返回错误: errcode={}, errmsg={}", errCode, errMsg);
                throw new RuntimeException(String.format("微信API错误[%d]: %s", errCode, errMsg));
            }
        }
        
        // 成功生成小程序码
        String base64Image = "data:image/png;base64," + Base64.getEncoder().encodeToString(qrCodeBytes);
        log.info("✅ 微信官方小程序码生成成功! 图片大小: {} bytes", qrCodeBytes.length);
        return base64Image;
    }
    
    /**
     * 处理小程序码生成失败的情况
     */
    private String handleMiniProgramCodeFailure(String scene, String targetPage, Exception originalError) {
        log.warn("主要生成方案失败，尝试降级方案...");
        
        // 降级方案1：使用createwxaqrcode接口
        try {
            log.info("降级方案1: 使用createwxaqrcode接口");
            String fallbackPagePath = targetPage + "?scene=" + scene;
            return generateWithCreateCode(fallbackPagePath);
        } catch (Exception e1) {
            log.warn("降级方案1失败: {}", e1.getMessage());
            
            // 降级方案2：使用默认页面
            try {
                log.info("降级方案2: 使用默认页面");
                String defaultPagePath = "pages/auth/phone-auth?scene=" + scene;
                return generateWithCreateCode(defaultPagePath);
            } catch (Exception e2) {
                log.warn("降级方案2失败: {}", e2.getMessage());
                
                // 最终降级：返回详细错误信息的占位符
                log.error("所有降级方案都失败，返回错误占位符");
                return generateErrorPlaceholder("微信小程序码生成失败", originalError.getMessage());
            }
        }
    }
    
    /**
     * 使用createwxaqrcode接口生成小程序码
     */
    private String generateWithCreateCode(String pagePath) throws Exception {
        String accessToken = getAccessToken();
        if (accessToken == null) {
            throw new RuntimeException("无法获取微信Access Token");
        }
        
        String apiUrl = String.format("https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=%s", accessToken);
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("path", pagePath);
        requestBody.put("width", 430);
        
        log.info("createwxaqrcode API请求: {}", requestBody);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        
        byte[] qrCodeBytes = restTemplate.postForObject(apiUrl, request, byte[].class);
        
        if (qrCodeBytes == null || qrCodeBytes.length == 0) {
            throw new RuntimeException("微信API返回空数据");
        }
        
        // 检查错误响应
        String responseStr = new String(qrCodeBytes, 0, Math.min(200, qrCodeBytes.length));
        if (responseStr.startsWith("{")) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode errorNode = objectMapper.readTree(responseStr);
            if (errorNode.has("errcode")) {
                int errCode = errorNode.get("errcode").asInt();
                String errMsg = errorNode.get("errmsg").asText();
                throw new RuntimeException(String.format("微信API错误[%d]: %s", errCode, errMsg));
            }
        }
        
        String base64Image = "data:image/png;base64," + Base64.getEncoder().encodeToString(qrCodeBytes);
        log.info("✅ 使用createwxaqrcode生成成功! 图片大小: {} bytes", qrCodeBytes.length);
        return base64Image;
    }
    
    /**
     * 验证小程序页面路径是否有效
     */
    private boolean isValidMiniProgramPage(String pagePath) {
        if (pagePath == null || pagePath.trim().isEmpty()) {
            return false;
        }
        
        // 有效的小程序页面路径列表
        String[] validPages = {
            "pages/auth/visitor-apply",
            "pages/auth/phone-auth",
            "pages/reservation/form",
            "pages/reservation/index"
        };
        
        for (String validPage : validPages) {
            if (pagePath.equals(validPage)) {
                return true;
            }
        }
        
        log.warn("页面路径 '{}' 不在有效页面列表中", pagePath);
        return false;
    }
    
    /**
     * 获取微信Access Token
     */
    private String getAccessToken() {
        try {
            String tokenUrl = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", 
                                           wechatConfig.getAppId(), wechatConfig.getSecret());
            
            log.info("请求微信Token URL: {}", tokenUrl.replaceAll("secret=[^&]+", "secret=***"));
            
            String tokenResponse = restTemplate.getForObject(tokenUrl, String.class);
            log.info("微信Token响应: {}", tokenResponse);
            
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode tokenNode = objectMapper.readTree(tokenResponse);
            
            if (tokenNode.has("errcode") && tokenNode.get("errcode").asInt() != 0) {
                int errCode = tokenNode.get("errcode").asInt();
                String errMsg = tokenNode.get("errmsg").asText();
                log.error("获取微信Access Token失败: errcode={}, errmsg={}", errCode, errMsg);
                return null;
            }
            
            String accessToken = tokenNode.get("access_token").asText();
            log.info("✅ 成功获取微信Access Token (长度: {})", accessToken.length());
            return accessToken;
            
        } catch (Exception e) {
            log.error("获取微信Access Token异常", e);
            return null;
        }
    }
    
    /**
     * 构建场景值（最多32个字符，避免中文字符）
     */
    private String buildSceneValue(String butlerPhone, String community, 
                                 String province, String city, String district) {
        try {
            // 提取手机号后8位
            String phoneShort = butlerPhone.length() >= 8 ? 
                              butlerPhone.substring(butlerPhone.length() - 8) : butlerPhone;
            
            // 生成时间戳
            String timestamp = String.valueOf(System.currentTimeMillis()).substring(8); // 后5位
            
            // 避免使用中文字符，使用简化的编码方案
            String locationCode = generateLocationCode(community, province, city, district);
            
            // 构建场景值: bp=手机号&lc=地区编码&t=时间戳
            String scene = String.format("bp=%s&lc=%s&t=%s", phoneShort, locationCode, timestamp);
            
            // 确保不超过32个字符
            if (scene.length() > 32) {
                scene = String.format("bp=%s&t=%s", phoneShort, timestamp);
            }
            
            log.info("场景值构建成功: {} (长度: {})", scene, scene.length());
            return scene;
            
        } catch (Exception e) {
            log.error("构建场景值失败", e);
            // 降级方案：只使用手机号
            String phoneShort = butlerPhone.length() >= 8 ? 
                              butlerPhone.substring(butlerPhone.length() - 8) : butlerPhone;
            return "bp=" + phoneShort;
        }
    }
    
    /**
     * 根据地区信息生成简短的数字编码
     */
    private String generateLocationCode(String community, String province, String city, String district) {
        StringBuilder sb = new StringBuilder();
        
        // 使用字符串哈希码的简化版本
        if (community != null && !community.isEmpty()) {
            sb.append(Math.abs(community.hashCode() % 1000));
        } else if (district != null && !district.isEmpty()) {
            sb.append(Math.abs(district.hashCode() % 1000));
        } else if (city != null && !city.isEmpty()) {
            sb.append(Math.abs(city.hashCode() % 1000));
        } else if (province != null && !province.isEmpty()) {
            sb.append(Math.abs(province.hashCode() % 1000));
        } else {
            sb.append("999"); // 默认编码
        }
        
        // 确保是3位数字
        String code = sb.toString();
        while (code.length() < 3) {
            code = "0" + code;
        }
        
        return code.substring(0, 3); // 只取前3位
    }
    
    /**
     * 生成微信小程序码（使用createwxaqrcode接口，支持完整页面路径）
     * @param pagePath 完整的页面路径（包含参数）
     * @return Base64编码的小程序码图片
     */
    public String generateWechatMiniProgramCode(String pagePath) {
        try {
            return generateWithCreateCode(pagePath);
        } catch (Exception e) {
            log.error("生成小程序码失败", e);
            return generateErrorPlaceholder("小程序码生成失败", e.getMessage());
        }
    }
    
    /**
     * 生成包含错误信息的占位符二维码
     */
    private String generateErrorPlaceholder(String errorType, String errorMessage) {
        log.warn("生成错误占位符: {} - {}", errorType, errorMessage);
        
        // 构建包含错误信息的JSON数据
        Map<String, Object> errorInfo = new HashMap<>();
        errorInfo.put("type", "error_placeholder");
        errorInfo.put("errorType", errorType);
        errorInfo.put("errorMessage", errorMessage);
        errorInfo.put("timestamp", System.currentTimeMillis());
        errorInfo.put("suggestion", "请检查微信小程序配置或联系技术支持");
        
        // 返回一个简单的错误占位符图片
        String placeholderBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==";
        
        return "data:image/png;base64," + placeholderBase64;
    }
    
    /**
     * 生成占位符二维码（开发测试用）
     */
    private String generatePlaceholderQrCode() {
        return generateErrorPlaceholder("开发模式", "微信小程序码功能未完全配置");
    }
} 