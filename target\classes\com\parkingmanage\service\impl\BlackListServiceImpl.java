package com.parkingmanage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.parkingmanage.entity.BlackList;
import com.parkingmanage.entity.MonthTick;
import com.parkingmanage.mapper.BlackListMapper;
import com.parkingmanage.service.BlackListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Service
public class BlackListServiceImpl extends ServiceImpl<BlackListMapper, BlackList> implements BlackListService {

    @Override
    public List<BlackList> queryInfoOnly(String parkName, String carCode, String specialCarTypeConfigName, String userName, String blackReason, String remark1, String remark2) {
        QueryWrapper<BlackList> queryWrapper = new QueryWrapper<>();
        // 模糊搜索部分
        queryWrapper.like(StringUtils.isNotBlank(carCode), "car_code", carCode);
        queryWrapper.like(StringUtils.isNotBlank(userName), "owner", userName);
        queryWrapper.like(StringUtils.isNotBlank(blackReason), "reason", blackReason);
        queryWrapper.like(StringUtils.isNotBlank(remark1), "remark1", remark1);
        queryWrapper.like(StringUtils.isNotBlank(remark2), "remark2", remark2);
        queryWrapper.eq(StringUtils.isNotBlank(parkName), "park_name", parkName);
        queryWrapper.eq(StringUtils.isNotBlank(specialCarTypeConfigName), "special_car_type_config_name", specialCarTypeConfigName);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<BlackList> findOne(BlackList blackList) {
        LambdaQueryWrapper<BlackList> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BlackList::getCarCode, blackList.getCarCode()).eq(BlackList::getReason, blackList.getReason())
                .eq(BlackList::getParkName, blackList.getParkName()).eq(BlackList::getRemark1, blackList.getRemark1())
                .eq(BlackList::getRemark2, blackList.getRemark2()).eq(BlackList::getBlackListForeverFlag, blackList.getBlackListForeverFlag()).
                eq(BlackList::getOwner, blackList.getOwner()).eq(BlackList::getSpecialCarTypeConfigName, blackList.getSpecialCarTypeConfigName());
        return baseMapper.selectList(queryWrapper);
    }
}
